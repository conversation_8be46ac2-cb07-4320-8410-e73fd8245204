package com.lirong.framework.config;

import io.undertow.server.DefaultByteBufferPool;
import io.undertow.websockets.jsr.WebSocketDeploymentInfo;
import org.springframework.boot.web.embedded.undertow.UndertowServletWebServerFactory;
import org.springframework.boot.web.server.WebServerFactoryCustomizer;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;

/**
 * Undertow服务器配置
 * 
 * <AUTHOR>
 */
@Configuration
public class UndertowConfig implements WebServerFactoryCustomizer<UndertowServletWebServerFactory> {

    /**
     * 初始化临时目录
     */
    @PostConstruct
    public void initTempDirectory() {
        try {
            // 创建临时目录
            String tempDir = System.getProperty("java.io.tmpdir");
            Path uploadTempDir = Paths.get(tempDir, "webdp-upload");
            
            if (!Files.exists(uploadTempDir)) {
                Files.createDirectories(uploadTempDir);
                System.out.println("Created upload temp directory: " + uploadTempDir.toString());
            }
            
            // 设置系统属性，确保Undertow使用正确的临时目录
            System.setProperty("java.io.tmpdir", tempDir);
            
        } catch (IOException e) {
            System.err.println("Failed to create upload temp directory: " + e.getMessage());
            e.printStackTrace();
        }
    }

    @Override
    public void customize(UndertowServletWebServerFactory factory) {
        // 配置Undertow服务器
        factory.addBuilderCustomizers(builder -> {
            // 设置缓冲区池
            builder.setByteBufferPool(new DefaultByteBufferPool(false, 1024));
        });
        
        // 配置部署信息
        factory.addDeploymentInfoCustomizers(deploymentInfo -> {
            // 设置临时目录
            String tempDir = System.getProperty("java.io.tmpdir");
            File uploadTempDir = new File(tempDir, "webdp-upload");
            if (!uploadTempDir.exists()) {
                uploadTempDir.mkdirs();
            }
            deploymentInfo.setTempDir(uploadTempDir);
            
            // 配置WebSocket
            WebSocketDeploymentInfo webSocketDeploymentInfo = new WebSocketDeploymentInfo();
            deploymentInfo.addServletContextAttribute("io.undertow.websockets.jsr.WebSocketDeploymentInfo", webSocketDeploymentInfo);
        });
    }
}
