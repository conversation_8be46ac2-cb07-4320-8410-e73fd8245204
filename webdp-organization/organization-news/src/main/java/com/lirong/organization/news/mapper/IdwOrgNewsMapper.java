package com.lirong.organization.news.mapper;

import java.util.List;

import com.lirong.organization.news.domain.IdwOrgNews;
import org.apache.ibatis.annotations.Param;

/**
 * 机构新闻报道Mapper接口
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
public interface IdwOrgNewsMapper {
    /**
     * 查询机构新闻报道
     *
     * @param newsId    机构新闻报道ID
     * @return 机构新闻报道
     */
    public IdwOrgNews selectIdwOrgNewsById(@Param("newsId") Long newsId);

    /**
     * 查询机构新闻报道列表
     *
     * @param idwOrgNews 机构新闻报道
     * @return 机构新闻报道集合
     */
    public List<IdwOrgNews> selectIdwOrgNewsList(IdwOrgNews idwOrgNews);

    /**
     * 新增机构新闻报道
     *
     * @param idwOrgNews 机构新闻报道
     * @return 结果
     */
    public int insertIdwOrgNews(IdwOrgNews idwOrgNews);

    /**
     * 修改机构新闻报道
     *
     * @param idwOrgNews 机构新闻报道
     * @return 结果
     */
    public int updateIdwOrgNews(IdwOrgNews idwOrgNews);

    /**
     * 删除机构新闻报道
     *
     * @param newsId 机构新闻报道ID
     * @return 结果
     */
    public int deleteIdwOrgNewsById(Long newsId);

    /**
     * 批量删除机构新闻报道
     *
     * @param newsIds   需要删除的数据ID
     * @param loginName 当前登录用户
     * @return 结果
     */
    public int deleteIdwOrgNewsByIds(@Param("newsIds") String[] newsIds, @Param("loginName") String loginName);

    /**
     * 根据机构编码删除
     *
     * @param orgCodes   机构编码
     * @param loginName  当前登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    public int deleteOrgNewsByOrgCodes(@Param("orgCodes") String[] orgCodes, @Param("loginName") String loginName, @Param("deleteTime") String deleteTime);

    /**
     * 根据机构编码查询
     *
     * @param orgCode 机构编码
     * @return 结果
     */
    public List<IdwOrgNews> selectByOrgCode(String orgCode);

    /**
     * 根据机构编码&标题&副标题&链接地址查询
     *
     * @param orgCode    机构编码
     * @param titleCn    中文标题
     * @param titleEn    英文标题
     * @param subtitleCn 中文副标题
     * @param subtitleEn 英文副标题
     * @param url        链接地址
     * @return 结果
     */
    public IdwOrgNews selectByTitleAndSubtitleAndUrl(@Param("orgCode") String orgCode, @Param("titleCn") String titleCn, @Param("titleEn") String titleEn, @Param("subtitleCn") String subtitleCn, @Param("subtitleEn") String subtitleEn, @Param("url") String url);

    /**
     * 获取文件路径
     *
     * @return 结果
     */
    public List<String> selectAllFilePath();

    /**
     * 根据机构编码查询
     *
     * @param orgCodes  机构编码
     * @return 结果
     */
    public List<IdwOrgNews> selectByOrgCodes(@Param("orgCodes") String[] orgCodes);

    /**
     * 新增新闻事件标签
     *
     * @param newsId   新闻ID
     * @param tagId    事件标签ID
     * @param userName 当前登录用户
     * @return 结果
     */
    public int insertOrgNewsTag(@Param("newsId") Long newsId, @Param("tagId") Long tagId, @Param("userName") String userName);
}
