package com.lirong.organization.staff.service.impl;

import java.util.*;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.common.utils.file.ImageUtils;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.organization.staff.domain.StaffRelevance;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.organization.staff.mapper.IdwOrgStaffMapper;
import com.lirong.organization.staff.domain.IdwOrgStaff;
import com.lirong.organization.staff.service.IdwOrgStaffService;
import com.lirong.common.core.text.Convert;

/**
 * 机构人员Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-02-03
 */
@Service
public class IdwOrgStaffServiceImpl implements IdwOrgStaffService {
    @Autowired
    private IdwOrgStaffMapper idwOrgStaffMapper;
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;
    @Autowired//人员
    private IdwPeopleMainMapper idwPeopleMainMapper;

    /**
     * 查询机构人员
     *
     * @param staffId 机构人员ID
     * @return 机构人员
     */
    @Override
    public IdwOrgStaff selectIdwOrgStaffById(Long staffId) {
        return idwOrgStaffMapper.selectIdwOrgStaffById(staffId);
    }

    /**
     * 查询没有关联的机构人员数据
     *
     * @param staffRelevance 查询参数
     * @return 结果
     */
    @Override
    public List<StaffRelevance> selectNotRelevanceOrgStaff(StaffRelevance staffRelevance) {
        return idwOrgStaffMapper.selectNotRelevanceOrgStaff(staffRelevance);
    }

    /**
     * 查询机构人员列表
     *
     * @param idwOrgStaff 机构人员
     * @return 机构人员
     */
    @Override
    public List<IdwOrgStaff> selectIdwOrgStaffList(IdwOrgStaff idwOrgStaff) {
        return idwOrgStaffMapper.selectIdwOrgStaffList(idwOrgStaff);
    }

    /**
     * 新增机构人员
     *
     * @param idwOrgStaff 机构人员
     * @return 结果
     */
    @Override
    public int insertIdwOrgStaff(IdwOrgStaff idwOrgStaff) {
        idwOrgStaff.setCreateBy(ShiroUtils.getUserName());
        idwOrgStaff.setCreateTime(DateUtils.getNowDate());
        return idwOrgStaffMapper.insertIdwOrgStaff(idwOrgStaff);
    }

    /**
     * 修改机构人员
     *
     * @param idwOrgStaff 机构人员
     * @return 结果
     */
    @Override
    public int updateIdwOrgStaff(IdwOrgStaff idwOrgStaff) {
        idwOrgStaff.setUpdateBy(ShiroUtils.getUserName());
        idwOrgStaff.setUpdateTime(DateUtils.getNowDate());
        return idwOrgStaffMapper.updateIdwOrgStaff(idwOrgStaff);
    }

    /**
     * 删除机构人员对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwOrgStaffByIds(String ids) {
        String loginName = ShiroUtils.getUserName();
        return idwOrgStaffMapper.deleteIdwOrgStaffByIds(Convert.toStrArray(ids), loginName);
    }

    /**
     * 删除机构人员信息
     *
     * @param staffId 机构人员ID
     * @return 结果
     */
    @Override
    public int deleteIdwOrgStaffById(Long staffId) {
        return idwOrgStaffMapper.deleteIdwOrgStaffById(staffId);
    }

    /**
     * 根据机构编码查询机构人员数据
     *
     * @param orgCode 机构编码
     * @return 结果
     */
    @Override
    public List<IdwOrgStaff> selectOrgStaffByOrgCode(String orgCode) {
        return idwOrgStaffMapper.selectOrgStaffByOrgCode(orgCode);
    }

    /**
     * 根据机构编码查询并排除某条数据
     *
     * @param orgCode 机构编码
     * @param staffId 需要排除数据的ID
     * @return 结果
     */
    @Override
    public List<IdwOrgStaff> selectByOrgCodeAndExcludeStaffId(String orgCode, Long staffId) {
        return idwOrgStaffMapper.selectByOrgCodeAndExcludeStaffId(orgCode, staffId);
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCode 机构编码
     * @return 结果
     */
    @Override
    public List<IdwOrgStaff> selectByOrgCode(String orgCode) {
        return idwOrgStaffMapper.selectByOrgCode(orgCode);
    }

    /**
     * 机构人员关联人员
     *
     * @param staffIds   机构人员ID
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public int relevancePeople(String staffIds, String peopleCode) {
        return idwOrgStaffMapper.relevancePeople(staffIds, peopleCode);
    }

    /**
     * 机构人员关联所有匹配人员
     *
     * @return 结果
     */
    @Override
    public int relevancePeopleAll() {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        List<StaffRelevance> staffRelevanceList = idwOrgStaffMapper.selectMayRelevancePeople();
        for (StaffRelevance staffRelevance : staffRelevanceList) {
            IdwOrgStaff orgStaff = new IdwOrgStaff();
            orgStaff.setStaffId(staffRelevance.getStaffId());
            orgStaff.setPeopleCode(staffRelevance.getPeopleCode());
            orgStaff.setPeopleType(staffRelevance.getPeopleType());
            orgStaff.setUpdateBy(userName);
            orgStaff.setUpdateTime(nowDate);
            idwOrgStaffMapper.updateIdwOrgStaff(orgStaff);
        }
        return 1;
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if (type.equals("org")) {
            return idwOrgStaffMapper.deleteOrgStaffByOrgCodes(codes, loginName, deleteTime);
        } else if (type.equals("people")) {
            return idwOrgStaffMapper.updatePeopleCodeByPeopleCode(codes, loginName);
        }
        return 1;
    }

    /**
     * 生成缩略图
     */
    @Override
    public void generateThumbnail() {
        List<IdwOrgStaff> staffList = idwOrgStaffMapper.selectAll();
        try {
            for (IdwOrgStaff staff : staffList) {
                if (StringUtils.isNotBlank(staff.getAvatar()) && !staff.getAvatar().contains(Constants.THUMBNAIL_PREFIX)) {
                    // 生成缩略图
                    String thumbnail = ImageUtils.generateThumbnail(staff.getAvatar());
                    // 更新缩略图属性
                    staff.setAvatar(thumbnail);
                    idwOrgStaffMapper.updateIdwOrgStaff(staff);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 校验Excel导入数据
     *
     * @param staffList        机构人员集合
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @param updateSupport    是否更新
     * @param title            标题
     * @return 结果
     */
    @Override
    public List<String> verifyImportOrgStaff(List<IdwOrgStaff> staffList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport, String title) {
        if (StringUtils.isNull(staffList) || staffList.size() < 1) {
            return null;
        }
        //处理完成机构人员数据列表
        List<IdwOrgStaff> treatingAfterOrgStaffList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> staffNameCnList = new ArrayList<>();
        List<String> staffNameEnList = new ArrayList<>();
        List<String> orgCodeList = (List<String>) CacheUtils.get("orgImportTreatingAfterOrgCodeList-" + userName);
        List<String> temporaryFilePathList = (List<String>) CacheUtils.get("orgImportTreatingTemporaryFilePathList-" + userName);//需要拷贝的文件路径
        if (StringUtils.isNull(temporaryFilePathList)) {
            temporaryFilePathList = new ArrayList<>();
        }
        List<String> newFilePathList = (List<String>) CacheUtils.get("orgImportTreatingNewFilePathList-" + userName);//拷贝文件目标路径
        if (StringUtils.isNull(newFilePathList)) {
            newFilePathList = new ArrayList<>();
        }
        for (IdwOrgStaff staff : staffList) {
            row++;
            if (StringUtils.isNotNull(staff)) {
                IdwOrgStaff orgStaff = new IdwOrgStaff();
                if (StringUtils.isBlank(staff.getOrgCode())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 机构编码为空");
                } else {
                    IdwOrg org = idwOrgMapper.selectOrgByOrgCode(staff.getOrgCode());
                    if (StringUtils.isNull(org) && (StringUtils.isNull(orgCodeList) || !orgCodeList.contains(staff.getOrgCode()))) {
                        isFailure = true;
                        msgList.add(title + ",第" + row + "行," + " 机构编码（" + staff.getOrgCode() + "）不存在");
                    } else {
                        //判断机构人员是否存在
                        orgStaff = idwOrgStaffMapper.selectByOrgCodeAndPeopleName(staff.getOrgCode(), staff.getPeopleNameCn(), staff.getPeopleNameEn(), (long) 0);
                        //当机构人员存在赋值机构人员ID 导入时直接判断ID是否为空即可
                        if (StringUtils.isNotNull(orgStaff)) {
                            staff.setStaffId(orgStaff.getStaffId());
                        }
                    }
                }
                if (StringUtils.isNotBlank(staff.getPeopleCode())) {
                    IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(staff.getPeopleCode());
                    if (StringUtils.isNull(people)) {
                        isFailure = true;
                        msgList.add(title + ",第" + row + "行," + " 人员编码（" + staff.getPeopleCode() + "）不存在");
                    }
                }
                if (StringUtils.isBlank(staff.getPeopleNameCn())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 中文名称为空");
                }
                if (StringUtils.isBlank(staff.getStatus())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 状态（在职，离职）为空或不存在");
                }
                if (StringUtils.isBlank(staff.getSource())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 数据来源为空");
                }
                staffNameCnList.add(staff.getPeopleNameCn());
                if (StringUtils.isNotBlank(staff.getPeopleNameEn())) {
                    staffNameEnList.add(staff.getPeopleNameEn());
                }
                //判断上级人员是否存在
                if (StringUtils.isNotBlank(staff.getManagerStaffName())) {
                    IdwOrgStaff parentStaff = idwOrgStaffMapper.selectByOrgCodeAndName(staff.getOrgCode(), staff.getManagerStaffName());
                    //库中和Excel都没有上级人员名称 提示
                    if (StringUtils.isNull(parentStaff) && !staffNameCnList.contains(staff.getManagerStaffName()) && !staffNameEnList.contains(staff.getManagerStaffName())) {
                        isFailure = true;
                        msgList.add(title + ",第" + row + "行," + " 上级人员（" + staff.getManagerStaffName() + "）不存在");
                    }
                }
                //校验图片
                String originalAvatar = staff.getAvatar();
                //替换文件名称中的特殊字符
                originalAvatar = FileUploadUtils.replaceFileNameSpecialCharacter(originalAvatar).toLowerCase();
                if (StringUtils.isNotBlank(originalAvatar)) {
                    if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                        Integer index = filePathIndexMap.get(originalAvatar);
                        //不存在 匹配文件名称
                        if (StringUtils.isNull(index)) {
                            String excelFileName = StringUtils.getFileName(originalAvatar);
                            //使用文件名称匹配
                            for (String key : filePathIndexMap.keySet()) {
                                String fileName = StringUtils.getFileName(key);
                                if (excelFileName.equals(fileName)) {
                                    index = filePathIndexMap.get(key);
                                    break;
                                }
                            }
                        }
                        if (StringUtils.isNotNull(index)) {
                            //数据存在 支持更新 或 数据不存在
                            if ((StringUtils.isNotNull(orgStaff) && updateSupport) || StringUtils.isNull(orgStaff)) {
                                String temporaryFilePath = filePathList.get(index);
                                String[] filePathArr = temporaryFilePath.split("/");
                                //根据文件名称与纬度路径生成新的文件路径
                                String newFilePath = FileUploadUtils.generateFilePath(WebdpConfig.getOrganizationPath(), filePathArr[filePathArr.length - 1]);
                                //保存文件路径
                                temporaryFilePathList.add(temporaryFilePath);
                                //保存新的文件路径
                                newFilePathList.add(newFilePath);
                                //赋值新图片路径
                                staff.setAvatar(newFilePath);
                            }
                        } else {
                            isFailure = true;
                            msgList.add(title + ",第" + row + "行," + " 头像不存在");
                        }
                    } else {
                        isFailure = true;
                        msgList.add(title + ",第" + row + "行," + " 头像不为空且压缩包文件为空");
                    }
                }
                treatingAfterOrgStaffList.add(staff);
            }
        }
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("orgImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
            CacheUtils.put("orgImportTreatingNewFilePathList-" + userName, newFilePathList);
            CacheUtils.put("orgImportTreatingAfterOrgStaffList-" + userName, treatingAfterOrgStaffList);
        }
        return null;
    }

    /**
     * 导入机构人员信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param userName      操作用户
     * @param title         标题
     * @return 结果
     */
    @Override
    public String importOrgStaff(boolean updateSupport, String userName, String title) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwOrgStaff> staffList = (List<IdwOrgStaff>) CacheUtils.get("orgImportTreatingAfterOrgStaffList-" + userName);
        if (StringUtils.isNull(staffList) || staffList.size() < 1) {
            return null;
        }
        for (IdwOrgStaff orgStaff : staffList) {
            if (StringUtils.isNotNull(orgStaff)) {
                IdwOrg org = idwOrgMapper.selectOrgByOrgCode(orgStaff.getOrgCode());
                //赋值机构编码
                orgStaff.setOrgCode(org.getOrgCode());
                if (StringUtils.isNotBlank(orgStaff.getManagerStaffName())) {
                    IdwOrgStaff managerStaff = idwOrgStaffMapper.selectByOrgCodeAndName(orgStaff.getOrgCode(), orgStaff.getManagerStaffName());
                    if (StringUtils.isNotNull(managerStaff))
                    //赋值上级人员id
                    {
                        orgStaff.setManagerStaffId(managerStaff.getStaffId());
                    }
                }
                if (StringUtils.isNull(orgStaff.getStaffId())) {
                    //新增
                    insertCount++;
                    orgStaff.setCreateBy(userName);
                    orgStaff.setCreateTime(nowDate);
                    idwOrgStaffMapper.insertIdwOrgStaff(orgStaff);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    orgStaff.setUpdateBy(userName);
                    orgStaff.setUpdateTime(nowDate);
                    idwOrgStaffMapper.updateIdwOrgStaff(orgStaff);
                }
            }
        }
        CacheUtils.remove("orgImportTreatingAfterOrgStaffList-" + userName);
        return title + "共：" + staffList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 批量更新机构人员头像
     *
     * @param fileNameList 文件名称
     * @param filePathList 文件路径
     * @return 结果
     */
    @Override
    public String batchUpdateAvatar(List<String> fileNameList, List<String> filePathList) {
        StringBuilder notEqualsFilerName = new StringBuilder();//未匹配的文件名称
        String userName = ShiroUtils.getUserName();
        for (int i = 0; i < fileNameList.size(); i++) {
            String fileName = fileNameList.get(i);
            String filePath = filePathList.get(i);
            //查询文件名称是否存在
            int count = idwOrgStaffMapper.selectCountByAvatar(fileName, userName);
            if (count > 0) {
                idwOrgStaffMapper.updateAvatarByFileName(fileName, filePath, userName);
            } else {
                FileUtils.deleteFile(WebdpConfig.getPath() + filePath.replace(Constants.RESOURCE_PREFIX, ""));
                notEqualsFilerName.append("<br/>").append(i + 1).append("、").append(fileName);
            }
        }
        return !notEqualsFilerName.toString().equals("") ? notEqualsFilerName.insert(0, "未匹配的文件名称如下：").toString() : notEqualsFilerName.toString();
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    @Override
    public List<IdwOrgStaff> selectByOrgCodes(String[] orgCodes) {
        return idwOrgStaffMapper.selectByOrgCodes(orgCodes);
    }

    /**
     * 根据文件名称修改文件路径
     *
     * @param type     业务类型 organization/personnel/weaponry
     * @param fileName 文件名称
     * @param fileUrl  文件路径
     * @param userName 当前登录用户
     * @return 结果
     */
    @Override
    public int accordingFileNameModificationFileUrl(String type, String fileName, String fileUrl, String userName) {
        if (type.equals("organization")) {
            int count = idwOrgStaffMapper.selectCountByAvatar(fileName, ShiroUtils.getUserName());
            if (count > 0) {
                idwOrgStaffMapper.updateAvatarByFileName(fileName, fileUrl, userName);
                return 1;
            }
        }
        return 0;
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.contains("organization")) {
            return idwOrgStaffMapper.selectAllFilePath();
        } else {
            return null;
        }
    }
}
