package com.lirong.organization.administration.service.impl;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.service.FileMatchingService;
import com.lirong.common.utils.*;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.organization.administration.domain.IdwAdministration;
import com.lirong.organization.administration.mapper.IdwAdministrationMapper;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.organization.administration.service.IdwAdministrationService;

import java.util.*;

/**
 * 政府机构Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-03-16
 */
@Service
public class IdwAdministrationServiceImpl implements IdwAdministrationService {
    @Autowired//政府机构
    private IdwAdministrationMapper idwAdministrationMapper;
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;
    @Autowired//文件匹配
    private List<FileMatchingService> fileMatchingServices;

    /**
     * 机构相关文件上传保存
     *
     * @param fileName 文件名称
     * @param fileUrl  文件路径
     * @return 结果
     */
    @Override
    public String saveBatchUploadFile(String fileName, String fileUrl) {
        StringBuilder notEqualsFilerName = new StringBuilder();//未匹配的文件名称
        int row = 1;
        if (StringUtils.isNotEmpty(fileName) && StringUtils.isNotEmpty(fileUrl)) {
            String userName = ShiroUtils.getUserName();
            String[] fileNameArray = fileName.split(":_:_:");
            String[] fileUrlArray = fileUrl.split(":_:_:");
            for (int i = 0; i < fileNameArray.length; i++) {
                String name = fileNameArray[i];
                String url = fileUrlArray[i];
                //校验文件名称是否存在
                int count = idwOrgMapper.selectCountByAvatar(name, userName);
                if (count > 0) {
                    idwOrgMapper.updateAvatarByFileName(name, url, userName);
                } else {
                    int matchingCount = 0;
                    for (FileMatchingService fileMatchingService : fileMatchingServices) {
                        int result = fileMatchingService.accordingFileNameModificationFileUrl("organization", name, url, userName);
                        if (result > 0) {
                            matchingCount++;
                        }
                    }
                    if (matchingCount == 0) {
                        //根据文件路径删除
                        FileUtils.deleteFile(WebdpConfig.getPath() + url.replace(Constants.RESOURCE_PREFIX, ""));
                        notEqualsFilerName.append("<br/>").append(row).append("、").append(name);
                        row++;
                    }
                }
            }
        }
        //获取没有匹配上的文件名称
        return !notEqualsFilerName.toString().equals("") ? notEqualsFilerName.insert(0, "未匹配的文件名称如下：").toString() : notEqualsFilerName.toString();
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    @Override
    public List<IdwAdministration> selectByOrgCodes(String[] orgCodes) {
        List<IdwAdministration> administrationList = idwAdministrationMapper.selectByOrgCodes(orgCodes);
        if (administrationList.size() < 1) {
            return administrationList;
        }
        //转换军兵种
        for (IdwAdministration administration : administrationList) {
            String troopsCategorys = administration.getTroopsCategory();
            if (StringUtils.isNotBlank(troopsCategorys)) {
                administration.setTroopsCategory(DictUtils.getDictLabel("sys_troops_categories", troopsCategorys, ",").replace(",", ";"));
            }
        }
        return administrationList;
    }

    /**
     * 导入校验数据格式
     *
     * @param administrationList 机构数据列表
     * @param filePathIndexMap   key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList       上传后的文件路径
     * @param administrationDir  临时文件夹目录
     * @param updateSupport      是否更新
     * @return 结果
     */
    @Override
    public List<String> verifyImportAdministration(List<IdwAdministration> administrationList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String administrationDir, boolean updateSupport) {
        if (StringUtils.isNull(administrationList) || administrationList.size() < 1) {
            return null;
        }
        String userName = ShiroUtils.getUserName();
        //处理完成政府机构数据列表
        List<IdwAdministration> treatingAfterOrgList = new ArrayList<>();
        //处理完成政府机构编码列表
        List<String> treatingAfterOrgCodeList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        //需要拷贝的文件路径
        List<String> temporaryFilePathList = new ArrayList<>();
        //拷贝文件目标路径
        List<String> newFilePathList = new ArrayList<>();
        List<String> msgList = new ArrayList<>();
        int maxOrderNum = idwOrgMapper.selectMaxOrderNum("政府机构");
        for (IdwAdministration administration : administrationList) {
            row++;
            if (StringUtils.isNotNull(administration)) {
                String orgCode = administration.getOrgCode();
                if (!treatingAfterOrgCodeList.contains(orgCode)) {
                    treatingAfterOrgCodeList.add(orgCode);
                } else {
                    isFailure = true;
                    msgList.add("政府机构,第" + row + "行," + " 机构编码在Excel中已存在");
                }
                if (StringUtils.isBlank(orgCode)) {
                    isFailure = true;
                    msgList.add("政府机构,第" + row + "行," + " 机构编码为空");
                }
                //判断国家
                if (StringUtils.isBlank(administration.getCountry())) {
                    isFailure = true;
                    msgList.add("政府机构,第" + row + "行," + " 国家/地区为空或不存在");
                }
                //判断名称
                if (StringUtils.isBlank(administration.getOrgNameCn())) {
                    isFailure = true;
                    msgList.add("政府机构,第" + row + "行," + " 中文名称为空");
                } else if (StringUtils.isBlank(administration.getTags())) {
                    if (StringUtils.isNotBlank(administration.getOrgNameEn())) {
                        administration.setTags(administration.getOrgNameCn() + "," + administration.getOrgNameEn());
                    } else {
                        administration.setTags(administration.getOrgNameCn());
                    }
                }
                //判断数据来源
                if (StringUtils.isBlank(administration.getSource())) {
                    isFailure = true;
                    msgList.add("政府机构,第" + row + "行," + " 数据来源为空");
                }
                //校验机构编码是否存在
                IdwOrg oldOrg = idwOrgMapper.selectOrgByOrgCode(orgCode);
                if (StringUtils.isNotNull(oldOrg)) {
                    //赋值机构ID
                    administration.setOrgId(oldOrg.getOrgId());
                    if (!(
                            administration.getOrgNameCn().equals(oldOrg.getOrgNameCn()) || administration.getOrgNameCn().equals(oldOrg.getOrgNameEn())
                    )
                            && !(
                            administration.getOrgNameEn().equals(oldOrg.getOrgNameCn()) || administration.getOrgNameEn().equals(oldOrg.getOrgNameEn())
                    )
                    ) {
                        isFailure = true;
                        msgList.add("政府机构,第" + row + "行," + " 机构编码（" + orgCode + "）在库中已存在");
                    }
                }
                //转换军兵种
                String troopsCategory = administration.getTroopsCategory();
                String newTroopsCategory = "";
                String errorTroopsCategory = "";
                if (StringUtils.isNotBlank(troopsCategory)) {
                    String[] troopsCategoryArr = troopsCategory.split("[;,；，]");
                    for (int i = 0; i < troopsCategoryArr.length; i++) {
                        String dictValue = DictUtils.getDictValue("sys_troops_categories", troopsCategoryArr[i].trim());
                        if (StringUtils.isNotBlank(dictValue)) {
                            if (StringUtils.isNotBlank(newTroopsCategory)) {
                                newTroopsCategory += "," + dictValue;
                            } else {
                                newTroopsCategory = dictValue;
                            }
                        } else {
                            if (StringUtils.isNotBlank(errorTroopsCategory)) {
                                errorTroopsCategory += "," + troopsCategoryArr[i];
                            } else {
                                errorTroopsCategory = troopsCategoryArr[i];
                            }
                        }
                    }
                    if (StringUtils.isNotBlank(errorTroopsCategory)) {
                        isFailure = true;
                        msgList.add("政府机构,第" + row + "行," + " 所属军兵种（" + errorTroopsCategory + "）不存在");
                    } else {
                        administration.setTroopsCategory(newTroopsCategory);
                    }
                }
                administration.setOrgType("政府机构");
                //赋值机构类型
                if (StringUtils.isBlank(administration.getOrgTypeAlias())) {
                    administration.setOrgTypeAlias("政府机构");
                }
                //排序号
                if (StringUtils.isNull(administration.getOrderNum())) {
                    administration.setOrderNum(maxOrderNum++);
                }
                //校验徽章
                String originalAvatar = administration.getAvatar();
                //替换文件名称中的特殊字符
                originalAvatar = FileUploadUtils.replaceFileNameSpecialCharacter(originalAvatar).toLowerCase();
                if (StringUtils.isNotBlank(originalAvatar)) {
                    if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                        Integer index = filePathIndexMap.get(originalAvatar);
                        //不存在 匹配文件名称
                        if (StringUtils.isNull(index)) {
                            String excelFileName = StringUtils.getFileName(originalAvatar);
                            //使用文件名称匹配
                            for (String key : filePathIndexMap.keySet()) {
                                String fileName = StringUtils.getFileName(key);
                                if (excelFileName.equals(fileName)) {
                                    index = filePathIndexMap.get(key);
                                    break;
                                }
                            }
                        }
                        if (StringUtils.isNotNull(index)) {
                            //当数据存在但是支持更新或数据不存在时处理图片
                            if ((StringUtils.isNotNull(oldOrg) && updateSupport) || StringUtils.isNull(oldOrg)) {
                                //获取文件名称
                                String temporaryFilePath = filePathList.get(index);
                                String[] filePathArr = temporaryFilePath.split("/");
                                //根据文件名称与纬度路径生成新的文件路径
                                String newFilePath = FileUploadUtils.generateFilePath(WebdpConfig.getPersonnelPath(), filePathArr[filePathArr.length - 1]);
                                //保存文件路径
                                temporaryFilePathList.add(temporaryFilePath);
                                //保存新的文件路径
                                newFilePathList.add(newFilePath);
                                //赋值新图片路径
                                administration.setAvatar(newFilePath);
                            }
                        } else {
                            isFailure = true;
                            msgList.add("政府机构,第" + row + "行," + " 徽章（" + originalAvatar + "）不存在");
                        }
                    } else {
                        isFailure = true;
                        msgList.add("政府机构,第" + row + "行," + " 徽章不为空且压缩包文件为空");
                    }
                }
                //校验成立时间
                String oldEstablishTime = administration.getEstablishTime();
                if (StringUtils.isNotBlank(oldEstablishTime)) {
                    String establishTime = DateUtils.updateDateSeparator(oldEstablishTime);
                    if (!DateUtils.isDate(establishTime) && establishTime.split("-").length == 3) {
                        isFailure = true;
                        msgList.add("政府机构,第" + row + "行," + " 成立时间（" + oldEstablishTime + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    administration.setEstablishTime(establishTime);
                }
                //格式化经纬度
                String latitude = administration.getLatitude();
                //纬度范围 -90~90
                if (StringUtils.isNotBlank(latitude)) {
                    if (latitude.contains("°")) {
                        latitude = CoordUtils.formatLAL(latitude);
                    }
                    int parseIntLatitude = 0;
                    try {
                        String[] latitudes = latitude.split("\\.");
                        //格式化
                        String latitudeInteger = latitudes[0];//纬度整数
                        String latitudeDecimals = latitudes.length > 1 ? latitudes[1] : "000000";//纬度小数
                        if (latitudeDecimals.length() > 6) {
                            latitude = latitudeInteger + "." + latitudeDecimals.substring(0, 6);
                        } else if (latitudeDecimals.length() < 6) {
                            latitude = latitudeInteger + "." + (latitudeDecimals + "000000").substring(0, 6);
                        }
                        //校验格式是否规范
                        boolean latitudeIsNumeric = StringUtils.validateNumber(latitude);
                        parseIntLatitude = Integer.parseInt(latitudeInteger);
                        if (!latitudeIsNumeric || !(90 > parseIntLatitude && parseIntLatitude > -90)) {
                            isFailure = true;
                            msgList.add("国防企业,第" + row + "行," + " 纬度（" + administration.getLatitude() + "）格式错误（纬度范围 -90~90，保留小数点后六位）");
                        } else {
                            administration.setLatitude(latitude);
                        }
                    } catch (NumberFormatException e) {
                        isFailure = true;
                        msgList.add("国防企业,第" + row + "行," + " 纬度（" + administration.getLatitude() + "）格式错误（纬度范围 -90~90，保留小数点后六位）");
                    }
                }
                String longitude = administration.getLongitude();
                //经度范围 -180~180
                if (StringUtils.isNotBlank(longitude)) {
                    if (longitude.contains("°")) {
                        longitude = CoordUtils.formatLAL(longitude);
                    }
                    int parseIntLongitude = 0;
                    try {
                        String[] longitudes = longitude.split("\\.");
                        //格式化
                        String longitudeInteger = longitudes[0];//经度整数
                        String longitudeDecimals = longitudes.length > 1 ? longitudes[1] : "000000";//经度小数
                        if (longitudeDecimals.length() > 6) {
                            longitude = longitudeInteger + "." + longitudeDecimals.substring(0, 6);
                        } else if (longitudeDecimals.length() < 6) {
                            longitude = longitudeInteger + "." + (longitudeDecimals + "000000").substring(0, 6);
                        }
                        //校验格式是否规范
                        boolean longitudeIsNumeric = StringUtils.validateNumber(longitude);
                        parseIntLongitude = Integer.parseInt(longitudes[0]);
                        if (!longitudeIsNumeric || !(180 > parseIntLongitude && parseIntLongitude > -180)) {
                            isFailure = true;
                            msgList.add("国防企业,第" + row + "行," + " 纬度（" + administration.getLongitude() + "）格式错误（经度范围 -180~180，保留小数点后六位）");
                        } else {
                            administration.setLongitude(longitude);
                        }
                    } catch (NumberFormatException e) {
                        isFailure = true;
                        msgList.add("国防企业,第" + row + "行," + " 纬度（" + administration.getLongitude() + "）格式错误（经度范围 -180~180，保留小数点后六位）");
                    }
                }
                treatingAfterOrgList.add(administration);
            }
        }

        CacheUtils.put("orgImportTreatingAfterOrgCodeList-" + userName, treatingAfterOrgCodeList);
        CacheUtils.put("orgImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
        CacheUtils.put("orgImportTreatingNewFilePathList-" + userName, newFilePathList);
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("orgImportTreatingAfterOrgList-" + userName, treatingAfterOrgList);
        }
        return null;
    }

    /**
     * 导入政府机构
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    @Override
    public String importAdministration(boolean updateSupport, String operName) {
        Date nowDate = DateUtils.getNowDate();
        Long userId = ShiroUtils.getUserId();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwAdministration> administrationList = (List<IdwAdministration>) CacheUtils.get("orgImportTreatingAfterOrgList-" + operName);
        if (StringUtils.isNull(administrationList) || administrationList.size() < 1) {
            return null;
        }
        for (IdwAdministration administration : administrationList) {
            if (StringUtils.isNotNull(administration)) {
                //校验机构是否存在
                if (StringUtils.isNull(administration.getOrgId())) {
                    //新增
                    insertCount++;
                    administration.setCreateBy(operName);
                    administration.setCreateTime(nowDate);
                    idwAdministrationMapper.insert(administration);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    administration.setUpdateBy(operName);
                    administration.setUpdateTime(nowDate);
                    idwAdministrationMapper.update(administration);
                }
            }
        }
        //清除政府机构数据列表缓存
        CacheUtils.remove("orgImportTreatingAfterOrgList-" + operName);
        return "政府机构共：" + administrationList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }
}
