package com.lirong.organization.product.service.impl;

import java.util.*;
import java.util.stream.Collectors;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.common.utils.uuid.IdUtils;
import com.lirong.multimedia.domain.IdwMultimedia;
import com.lirong.multimedia.service.IdwMultimediaService;
import com.lirong.organization.common.domain.IdwOrg;
import com.lirong.organization.common.mapper.IdwOrgMapper;
import com.lirong.organization.product.domain.IdwOrgProductTechnology;
import com.lirong.organization.product.mapper.IdwOrgProductTechnologyMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.organization.product.mapper.IdwOrgProductMapper;
import com.lirong.organization.product.domain.IdwOrgProduct;
import com.lirong.organization.product.service.IdwOrgProductService;
import com.lirong.common.core.text.Convert;
import org.springframework.transaction.annotation.Transactional;

/**
 * 机构研究成果Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-28
 */
@Service
@Transactional
public class IdwOrgProductServiceImpl implements IdwOrgProductService {
    public static final Logger log = LoggerFactory.getLogger(IdwOrgProductServiceImpl.class);
    @Autowired
    private IdwOrgProductMapper idwOrgProductMapper;
    @Autowired//机构产品关键技术
    private IdwOrgProductTechnologyMapper idwOrgProductTechnologyMapper;
    @Autowired//多媒体
    private IdwMultimediaService idwMultimediaService;
    @Autowired//组织机构
    private IdwOrgMapper idwOrgMapper;

    /**
     * 查询机构研究成果
     *
     * @param productId 机构研究成果ID
     * @return 机构研究成果
     */
    @Override
    public IdwOrgProduct selectIdwOrgProductById(Long productId) {
        IdwOrgProduct product = idwOrgProductMapper.selectIdwOrgProductById(productId);
        List<IdwOrgProductTechnology> productTechnologyList = idwOrgProductTechnologyMapper.selectByProductId(productId);
        product.setTechnology(productTechnologyList.stream().map(IdwOrgProductTechnology::getTechnologyName).collect(Collectors.joining(",")));
        return product;
    }

    /**
     * 查询机构研究成果列表
     *
     * @param idwOrgProduct 机构研究成果
     * @return 机构研究成果
     */
    @Override
    public List<IdwOrgProduct> selectIdwOrgProductList(IdwOrgProduct idwOrgProduct) {
        return idwOrgProductMapper.selectIdwOrgProductList(idwOrgProduct);
    }

    /**
     * 根据文件路径去重
     *
     * @param fileUrlArr  文件路径
     * @param fileNameArr 文件名称
     * @return 结果
     */
    public Map<String, List<String>> deleteArrayHeavy(String[] fileUrlArr, String[] fileNameArr) {
        List<String> newFileUrlList = new ArrayList<>();
        List<String> newFileNameList = new ArrayList<>();
        for (int i = 0; i < fileUrlArr.length; i++) {
            String fileUrl = fileUrlArr[i];
            String fileName = fileNameArr[i];
            if (!newFileUrlList.contains(fileUrl)) {
                newFileUrlList.add(fileUrl);
                newFileNameList.add(fileName);
            }
        }
        Map<String, List<String>> map = new HashMap<>();
        map.put("fileUrl", newFileUrlList);
        map.put("fileName", newFileNameList);
        return map;
    }

    /**
     * 新增多媒体
     *
     * @param idwOrgProduct 机构产品对象
     */
    public void insertIdwMultimedia(IdwOrgProduct idwOrgProduct) {
        String fileUrls = idwOrgProduct.getFileUrl();
        String fileNames = idwOrgProduct.getFileName();
        if (StringUtils.isNotEmpty(fileUrls)) {
            Map<String, List<String>> map = deleteArrayHeavy(fileUrls.split(","), fileNames.split(","));
            List<String> fileUrlArray = map.get("fileUrl");
            List<String> fileNameArray = map.get("fileName");
            for (int i = 0; i < fileUrlArray.size(); i++) {
                String fileUrl = fileUrlArray.get(i);
                String fileName = fileNameArray.get(i);
                IdwMultimedia multimedia = new IdwMultimedia();
                multimedia.setBusinessType(idwOrgProduct.getBusinessType());//类型 模块名称
                multimedia.setAssociationId(idwOrgProduct.getProductId().toString());//关联ID
                multimedia.setCreateBy(idwOrgProduct.getCreateBy());
                multimedia.setCreateTime(idwOrgProduct.getCreateTime());
                multimedia.setSource("用户上传");//数据来源
                //获取文件后缀
                if (fileName.contains(".") && fileName.lastIndexOf(".") != 0) {
                    multimedia.setMediaType(fileName.substring(fileName.lastIndexOf(".") + 1));
                }
                multimedia.setStoragePath(fileUrl);//存储路径
                multimedia.setTitle(fileName);//文件名称
                idwMultimediaService.insertIdwMultimedia(multimedia);
            }
        }
    }

    /**
     * 新增机构研究成果
     *
     * @param idwOrgProduct 机构研究成果
     * @return 结果
     */
    @Override
    public int insertIdwOrgProduct(IdwOrgProduct idwOrgProduct) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        idwOrgProduct.setCreateBy(userName);
        idwOrgProduct.setCreateTime(nowDate);
        String fileUrls = idwOrgProduct.getFileUrl();
        String fileNames = idwOrgProduct.getFileName();
        try {
            idwOrgProductMapper.insertIdwOrgProduct(idwOrgProduct);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("新增机构研究成果失败");
            return 0;
        }
        //保存多媒体
        idwOrgProduct.setFileUrl(fileUrls);
        idwOrgProduct.setFileName(fileNames);
        insertIdwMultimedia(idwOrgProduct);
        //保存关键技术
        String technology = idwOrgProduct.getTechnology();
        if (StringUtils.isNotBlank(technology)) {
            String[] technologys = technology.split(",");
            for (String name : technologys) {
                IdwOrgProductTechnology productTechnology = new IdwOrgProductTechnology();
                productTechnology.setProductId(idwOrgProduct.getProductId());
                productTechnology.setTechnologyName(name);
                productTechnology.setCreateBy(userName);
                productTechnology.setCreateTime(nowDate);
                idwOrgProductTechnologyMapper.insertIdwOrgProductTechnology(productTechnology);
            }
        }
        return 1;// 1 成功 0 失败
    }

    /**
     * 修改机构研究成果
     *
     * @param idwOrgProduct  机构研究成果
     * @param deleteMediaIds 删除的多媒体ID
     * @return 结果
     */
    @Override
    public int updateIdwOrgProduct(IdwOrgProduct idwOrgProduct, String deleteMediaIds) {
        String userName = ShiroUtils.getUserName();
        Date nowDate = DateUtils.getNowDate();
        idwOrgProduct.setUpdateBy(userName);
        idwOrgProduct.setUpdateTime(nowDate);
        String fileUrls = idwOrgProduct.getFileUrl();
        String fileNames = idwOrgProduct.getFileName();
        try {
            idwOrgProduct.setFileUrl("");
            idwOrgProduct.setFileName("");
            idwOrgProductMapper.updateIdwOrgProduct(idwOrgProduct);
        } catch (Exception e) {
            e.printStackTrace();
            log.info("修改机构研究成果失败");
            return 0;
        }
        //修改多媒体
        idwMultimediaService.deleteIdwMultimediaByIds(deleteMediaIds);//删除多媒体
        idwOrgProduct.setFileUrl(fileUrls);
        idwOrgProduct.setFileName(fileNames);
        insertIdwMultimedia(idwOrgProduct);
        //保存关键技术
        idwOrgProductTechnologyMapper.deleteByProductId(idwOrgProduct.getProductId());//根据产品ID删除
        String technology = idwOrgProduct.getTechnology();
        if (StringUtils.isNotBlank(technology)) {
            String[] technologys = technology.split(",");
            for (String name : technologys) {
                IdwOrgProductTechnology productTechnology = new IdwOrgProductTechnology();
                productTechnology.setProductId(idwOrgProduct.getProductId());
                productTechnology.setTechnologyName(name);
                productTechnology.setCreateBy(userName);
                productTechnology.setCreateTime(nowDate);
                idwOrgProductTechnologyMapper.insertIdwOrgProductTechnology(productTechnology);
            }
        }
        return 1;// 1 成功 0 失败
    }

    /**
     * 删除机构研究成果对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwOrgProductByIds(String ids) {
        String loginName = ShiroUtils.getUserName();
        return idwOrgProductMapper.deleteIdwOrgProductByIds(Convert.toStrArray(ids), loginName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param orgProductList   机构采办项目集合
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @param updateSupport    是否更新
     * @return 结果
     */
    @Override
    public List<String> verifyImportOrgProduct(List<IdwOrgProduct> orgProductList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport) {
        if (StringUtils.isNull(orgProductList) || orgProductList.size() < 1) {
            return null;
        }
        //处理完成研究成果数据列表
        List<IdwOrgProduct> treatingAfterOrgProductList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> orgCodeList = (List<String>) CacheUtils.get("orgImportTreatingAfterOrgCodeList-" + userName);
        List<String> temporaryFilePathList = (List<String>) CacheUtils.get("orgImportTreatingTemporaryFilePathList-" + userName);//需要拷贝的文件路径
        if (StringUtils.isNull(temporaryFilePathList)) {
            temporaryFilePathList = new ArrayList<>();
        }
        List<String> newFilePathList = (List<String>) CacheUtils.get("orgImportTreatingNewFilePathList-" + userName);//拷贝文件目标路径
        if (StringUtils.isNull(newFilePathList)) {
            newFilePathList = new ArrayList<>();
        }
        //需要删除的多媒体ID
        List<Long> deleteMultimediaIdList = new ArrayList<>();
        //key Excel中的行数 value 产品对应产品文档
        Map<Integer, List<String>> productFIlePathMap = new HashMap<>();
        for (IdwOrgProduct orgProduct : orgProductList) {
            row++;
            if (StringUtils.isNotNull(orgProduct)) {
                if (StringUtils.isBlank(orgProduct.getOrgCode())) {
                    isFailure = true;
                    msgList.add("产品&研究成果,第" + row + "行," + " 机构编码为空");
                } else {
                    String orgCode = orgProduct.getOrgCode();
                    IdwOrg org = idwOrgMapper.selectOrgByOrgCode(orgCode);
                    if (StringUtils.isNotNull(org)) {
                        orgProduct.setOrgCode(org.getOrgCode());
                    }
                    if (StringUtils.isNull(org) && (StringUtils.isNull(orgCodeList) || !orgCodeList.contains(orgCode))) {
                        isFailure = true;
                        msgList.add("产品&研究成果,第" + row + "行," + " 机构编码（" + orgCode + "）不存在");
                    }
                }
                if (StringUtils.isBlank(orgProduct.getField())) {
                    isFailure = true;
                    msgList.add("产品&研究成果,第" + row + "行," + " 产品领域为空");
                }
                if (StringUtils.isBlank(orgProduct.getSource())) {
                    isFailure = true;
                    msgList.add("产品&研究成果,第" + row + "行," + " 数据来源为空");
                }
                IdwOrgProduct product = new IdwOrgProduct();
                if (StringUtils.isBlank(orgProduct.getProductNameCn())) {
                    isFailure = true;
                    msgList.add("产品&研究成果,第" + row + "行," + " 中文名称为空");
                } else {
                    product = idwOrgProductMapper.selectFieldAndName(orgProduct.getOrgCode(), orgProduct.getField(), orgProduct.getProductNameCn(), orgProduct.getProductNameEn());
                    if (StringUtils.isNotNull(product)) {
                        orgProduct.setProductId(product.getProductId());
                    }
                }
                //校验图片
                String originalPicture = orgProduct.getPicture();
                //替换文件名称中的特殊字符
                originalPicture = FileUploadUtils.replaceFileNameSpecialCharacter(originalPicture).toLowerCase();
                if (StringUtils.isNotBlank(originalPicture)) {
                    if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                        Integer index = filePathIndexMap.get(originalPicture);
                        //不存在 匹配文件名称
                        if (StringUtils.isNull(index)) {
                            String excelFileName = StringUtils.getFileName(originalPicture);
                            //使用文件名称匹配
                            for (String key : filePathIndexMap.keySet()) {
                                String fileName = StringUtils.getFileName(key);
                                if (excelFileName.equals(fileName)) {
                                    index = filePathIndexMap.get(key);
                                    break;
                                }
                            }
                        }
                        if (StringUtils.isNotNull(index)) {
                            //数据存在 支持更新 或 数据不存在
                            if ((StringUtils.isNotNull(product) && updateSupport) || StringUtils.isNull(product)) {
                                String temporaryFilePath = filePathList.get(index);
                                String[] filePathArr = temporaryFilePath.split("/");
                                //根据文件名称与纬度路径生成新的文件路径
                                String newFilePath = FileUploadUtils.generateFilePath(WebdpConfig.getOrganizationPath(), filePathArr[filePathArr.length - 1]);
                                //保存文件路径
                                temporaryFilePathList.add(temporaryFilePath);
                                //保存新的文件路径
                                newFilePathList.add(newFilePath);
                                //赋值新图片路径
                                orgProduct.setPicture(newFilePath);
                            }
                        } else {
                            isFailure = true;
                            msgList.add("产品&研究成果,第" + row + "行," + " 缩略图不存在");
                        }
                    } else {
                        isFailure = true;
                        msgList.add("产品&研究成果,第" + row + "行," + " 缩略图不为空且压缩包文件为空");
                    }
                }
                //保存需要删除附件
                if (StringUtils.isNotNull(product) && updateSupport) {
                    List<IdwMultimedia> multimediaList = idwMultimediaService.selectByBusinessTypeAndAssociationIds("organization_product", orgProduct.getProductId().toString().split(","));
                    List<Long> mediaIdList = multimediaList.stream().map(IdwMultimedia::getMediaId).collect(Collectors.toList());
                    //保存原关联多媒体ID
                    deleteMultimediaIdList.addAll(mediaIdList);
                }

                //校验附件
                String originalFiles = orgProduct.getFileUrl();
                if (StringUtils.isNotBlank(originalFiles)) {
                    //数据存在 支持更新 或 数据不存在
                    if ((StringUtils.isNotNull(product) && updateSupport) || StringUtils.isNull(product)) {
                        if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                            String[] originalFileArr = originalFiles.split("[;；]");
                            //产品路径
                            List<String> newProductFIlePathList = new ArrayList<>();
                            String fileNames = "";
                            for (String originalFile : originalFileArr) {
                                fileNames = StringUtils.isBlank(fileNames) ? originalFile : fileNames + "," + originalFile;
                                //替换文件名称中的特殊字符
                                originalFile = FileUploadUtils.replaceFileNameSpecialCharacter(originalFile).toLowerCase();
                                Integer index = filePathIndexMap.get(originalFile);
                                //不存在 匹配文件名称
                                if (StringUtils.isNull(index)) {
                                    String excelFileName = StringUtils.getFileName(originalFile);
                                    //使用文件名称匹配
                                    for (String key : filePathIndexMap.keySet()) {
                                        String fileName = StringUtils.getFileName(key);
                                        if (excelFileName.equals(fileName)) {
                                            index = filePathIndexMap.get(key);
                                            break;
                                        }
                                    }
                                }
                                if (StringUtils.isNotNull(index)) {
                                    String filePath = filePathList.get(index);
                                    //获取文件路径
                                    String absolutePath = filePath.replace(Constants.RESOURCE_PREFIX, WebdpConfig.getProfile());
                                    String newAbsolutePath = absolutePath.replace(baseDir, WebdpConfig.getOrganizationPath());
                                    String[] newAbsolute = newAbsolutePath.split("/");
                                    String fileName = newAbsolute[newAbsolute.length - 1];
                                    //获取后缀
                                    String extension = fileName.substring(fileName.lastIndexOf(".") + 1);
                                    //生成UUID作为文件名称
                                    String newFileName = IdUtils.fastUUID() + "." + extension;
                                    if (fileName.contains(Constants.THUMBNAIL_PREFIX)) {
                                        newFileName = Constants.THUMBNAIL_PREFIX + newFileName;
                                    }
                                    newAbsolutePath = newAbsolutePath.replace(fileName, newFileName);
                                    String newFilePath = newAbsolutePath.replace(WebdpConfig.getProfile(), Constants.RESOURCE_PREFIX);
                                    //保存文件路径 校验完成并校验通过后统一导入
                                    temporaryFilePathList.add(absolutePath);
                                    newFilePathList.add(newAbsolutePath);
                                    //保存新图片路径
                                    newProductFIlePathList.add(newFilePath);
                                } else {
                                    isFailure = true;
                                    msgList.add("产品&研究成果,第" + row + "行," + " 产品文档（" + originalFile + "）不存在");
                                }
                            }
                            orgProduct.setFileName(fileNames);
                            //key 当前行数 value 产品文档路径列表
                            productFIlePathMap.put(row, newProductFIlePathList);
                        } else {
                            isFailure = true;
                            msgList.add("产品&研究成果,第" + row + "行," + " 产品文档不为空且压缩包文件为空");
                        }
                    }
                }
                orgProduct.setExcelRow(row);
                treatingAfterOrgProductList.add(orgProduct);
            }
        }
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("orgImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
            CacheUtils.put("orgImportTreatingNewFilePathList-" + userName, newFilePathList);
            CacheUtils.put("orgImportTreatingAfterOrgProductList-" + userName, treatingAfterOrgProductList);
            CacheUtils.put("orgImportTreatingDeleteMultimediaIdList-" + userName, deleteMultimediaIdList);
            CacheUtils.put("orgImportTreatingProductFIlePathMap-" + userName, productFIlePathMap);
        }
        return null;
    }

    /**
     * 导入机构产品&研究成果信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    @Override
    public String importOrgProduct(boolean updateSupport, String operName) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwOrgProduct> orgProductList = (List<IdwOrgProduct>) CacheUtils.get("orgImportTreatingAfterOrgProductList-" + operName);
        //需要删除的多媒体ID
        List<Long> deleteMultimediaIdList = (List<Long>) CacheUtils.get("orgImportTreatingDeleteMultimediaIdList-" + operName);
        //key Excel中的行数 value 产品对应产品文档
        Map<Integer, List<String>> productFIlePathMap = (Map<Integer, List<String>>) CacheUtils.get("orgImportTreatingProductFIlePathMap-" + operName);
        if (StringUtils.isNull(orgProductList) || orgProductList.size() < 1) {
            return null;
        }
        if (StringUtils.isNotNull(deleteMultimediaIdList) && deleteMultimediaIdList.size() > 0) {
            idwMultimediaService.deleteIdwMultimediaByIds(StringUtils.join(deleteMultimediaIdList, ","));
        }
        for (IdwOrgProduct orgProduct : orgProductList) {
            if (StringUtils.isNotNull(orgProduct)) {
                if (StringUtils.isNull(orgProduct.getProductId())) {
                    //新增
                    insertCount++;
                    orgProduct.setCreateBy(operName);
                    orgProduct.setCreateTime(nowDate);
                    idwOrgProductMapper.insertIdwOrgProduct(orgProduct);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    idwOrgProductTechnologyMapper.deleteByProductId(orgProduct.getProductId());//根据产品ID删除
                    orgProduct.setUpdateBy(operName);
                    orgProduct.setUpdateTime(nowDate);
                    idwOrgProductMapper.updateIdwOrgProduct(orgProduct);
                }
                Long productId = orgProduct.getProductId();
                List<String> newProductFIlePathList = productFIlePathMap.get(orgProduct.getExcelRow());
                if (StringUtils.isNotNull(newProductFIlePathList) && newProductFIlePathList.size() > 0) {
                    String[] fileNameArr = orgProduct.getFileName().split(",");
                    for (int i = 0; i < newProductFIlePathList.size(); i++) {
                        String filePath = newProductFIlePathList.get(i);
                        IdwMultimedia multimedia = new IdwMultimedia();
                        multimedia.setBusinessType("organization_product");
                        multimedia.setAssociationId(productId.toString());
                        multimedia.setCreateBy(operName);
                        multimedia.setCreateTime(nowDate);
                        multimedia.setMediaType(filePath.substring(filePath.lastIndexOf(".") + 1));
                        multimedia.setStoragePath(filePath);
                        multimedia.setTitle(fileNameArr[i]);
                        multimedia.setMd5(FileUtils.getMd5(filePath));
                        multimedia.setSource("用户上传");
                        idwMultimediaService.insertIdwMultimedia(multimedia);
                    }
                }
                //保存关键技术
                String technology = orgProduct.getTechnology();
                if (StringUtils.isNotBlank(technology)) {
                    String[] technologys = technology.split(",");
                    for (String name : technologys) {
                        IdwOrgProductTechnology productTechnology = new IdwOrgProductTechnology();
                        productTechnology.setProductId(orgProduct.getProductId());
                        productTechnology.setTechnologyName(name);
                        productTechnology.setCreateBy(operName);
                        productTechnology.setCreateTime(nowDate);
                        idwOrgProductTechnologyMapper.insertIdwOrgProductTechnology(productTechnology);
                    }
                }
            }
        }
        CacheUtils.remove("orgImportTreatingAfterOrgProductList-" + operName);
        return "产品&研究成果：" + orgProductList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据机构编码查询
     *
     * @param orgCodes 机构编码
     * @return 结果
     */
    @Override
    public List<IdwOrgProduct> selectByOrgCodes(String[] orgCodes) {
        return idwOrgProductMapper.selectByOrgCodes(orgCodes);
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if (type.equals("org")) {
            List<IdwOrgProduct> orgProductList = idwOrgProductMapper.selectByOrgCodes(codes);
            if (orgProductList.size() > 0) {
                List<String> productIdList = orgProductList.stream().map(p -> p.getProductId().toString()).collect(Collectors.toList());
                //删除产品文件
                idwMultimediaService.deleteByBusinessTypeAndAssociationIds("organization_product", productIdList.toArray(new String[0]), loginName, deleteTime);
            }
            return idwOrgProductMapper.deleteOrgProductByOrgCodes(codes, loginName, deleteTime);
        }
        return 1;
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.contains("organization")) {
            return idwOrgProductMapper.selectAllFilePath();
        } else {
            return null;
        }
    }
}
