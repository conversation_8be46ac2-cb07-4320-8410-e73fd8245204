<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改机构架构')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-architecture-edit" th:object="${idwOrgArchitecture}">
            <input name="architectureId" id="architectureId" th:field="*{architectureId}" type="hidden">
            <input name="parentId" id="parentId" th:field="*{parentId}" type="hidden">
            <input name="orgCode" th:field="*{orgCode}" type="hidden">
            <input name="ancestors" th:field="*{ancestors}" type="hidden">
            <input name="level" th:field="*{level}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">上级名称：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input name="parentName" onclick="architectureTree()" id="parentName" type="text" th:field="*{parentName}" class="form-control" readonly>
                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">中文名称：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="nodeOrgCode" name="nodeOrgCode" th:field="*{nodeOrgCode}" type="hidden">
                        <input id="nameCn" name="nameCn" th:field="*{nameCn}" placeholder="可通过单位名称搜索" class="form-control"
                               type="text">
                        <div class="input-group-btn">
                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                <span class="caret"></span>
                            </button>
                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">英文名称：</label>
                <div class="col-sm-8">
                    <input name="nameEn" id="nameEn" th:field="*{nameEn}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">类型：</label>
                <div class="col-sm-8">
                    <div class="radio check-box">
                        <label>
                            <input type="radio" checked="" value="部门" name="type" id="department">  部门</label>
                    </div>
                    <div class="radio check-box">
                        <label>
                            <input type="radio" value="机构" name="type" id="organization"> 机构</label>
                    </div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">职能：</label>
                <div class="col-sm-8">
                    <textarea name="function" class="form-control" rows="18">[[*{function}]]</textarea>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: bootstrap-suggest-js"/>
    <script th:inline="javascript">
        let prefix = ctx + "organization/architecture";
        $("#form-architecture-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-architecture-edit').serialize(), reset);
            }
        }

        function reset(){
            window.parent.reset()
        }

        let type = [[${idwOrgArchitecture.type}]]
        if (type == '机构'){
            $("#department").iCheck('uncheck');
            $("#organization").iCheck('check');
        }

        /*机构架构-新增-选择父部门树*/
        function architectureTree() {
            let orgCode = $("#orgCode").val()
            let architectureId = $("#architectureId").val()
            let options = {
                title: '组织架构选择',
                width: "380",
                url: prefix + "/architectureTree/" + orgCode + '/' + architectureId,
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            let body = layer.getChildFrame('body', index);
            $("#parentId").val(body.find('#parentId').val());
            $("#parentName").val(body.find('#parentName').val());
            layer.close(index);
        }

        let orgCommon = ctx + "organization/org"

        //加载驻扎单位搜索下拉框 机构名称 nameCn 查询中英文名称
        let peopleBsSuggest = $("#nameCn").bsSuggest({
            url: orgCommon + "/selectByKeywordAndIncludeOrgTypes?orgTypes=国防企业,国防科研&keyword=",
            //ignorecase : true,//搜索忽略大小写
            getDataMethod: 'url',//获取数据的方式，总是从 URL 获取
            autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
            idField: "orgCode",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
            keyField: "orgNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
            effectiveFields: ['orgNameCn', 'orgNameEn'],//设置展示字段
            effectiveFieldsAlias: {
                orgNameCn: '中文名称',
                orgNameEn: '英文名称'
            }//设置字段别名
        }).on('onSetSelectValue', function (e, selectedData, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
            //选择后隐藏下拉框
            $("#nameCn").bsSuggest("hide");
            $('#nodeOrgCode').val(result.orgCode);
            $('#nameEn').val(result.orgNameEn);
            $("#department").iCheck('uncheck');
            $("#organization").iCheck('check');
        }).on('onUnsetSelectValue', function () {//当设置了 idField，且自由输入内容时触发（与背景警告色显示同步）
            //console.log('onUnsetSelectValue');
        }).on('onShowDropdown', function (e, data) {// 下拉菜单显示时触发
            //console.log('onShowDropdown', e.target.value, data);
        }).on('onHideDropdown', function (e, data) {// //选中后或输入框失去焦点时，下拉框隐藏时触发
            //console.log('onHideDropdown', e.target.value, data);
        }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
            let background = $('#nameCn').css("background-color");
            if (background == 'rgba(255, 0, 0, 0.1)') {
                $('#nodeOrgCode').val('');
                $('#nameEn').val('');
                $("#organization").iCheck('uncheck');
                $("#department").iCheck('check');
                $('#nameCn').css("background-color", 'rgb(255, 255, 255)')
            }
        });
    </script>
</body>
</html>