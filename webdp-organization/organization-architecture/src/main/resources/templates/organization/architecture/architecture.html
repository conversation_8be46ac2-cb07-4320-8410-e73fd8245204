<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('机构架构列表')"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
    <style type="text/css">
        .bs-bars {
            margin-top: 10px !important;
            margin-bottom: 5px !important;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ui-layout-west">
    <div class="box box-main">
        <div class="box-header">
            <div class="box-title">
                <i class="fa icon-grid"></i>组织架构
            </div>
            <div class="box-tools pull-right">
                <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新"><i class="fa fa-refresh"></i>
                </button>
            </div>
        </div>
        <div class="ui-layout-content">
            <div id="tree" class="ztree"></div>
        </div>
    </div>
</div>

<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-architecture">
                    <div class="select-list">
                        <input type="hidden" name="orgCode" id="orgCode" th:value="${orgCode}">
                        <input type="hidden" name="parentId" id="parentId" value="0">
                        <ul>
                            <li>
                                <label>名称：</label>
                                <input type="text" name="nameCn" id="nameCn"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-architecture', 'bootstrap-table-architecture')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-architecture', 'bootstrap-table-architecture')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-architecture" role="group">
                <a class="btn btn-success" onclick="add()" shiro:hasPermission="organization:architecture:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-architecture"></table>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: footer"/>
<th:block th:include="include :: ztree-js"/>
<th:block th:include="include :: layout-latest-js"/>
<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('organization:architecture:edit')}]];
    let removeFlag = [[${@permission.hasPermi('organization:architecture:remove')}]];
    let prefix = ctx + "organization/architecture";

    let orgCode = [[${orgCode}]]
    let topName = [[${orgName}]]

    $(function () {
        let options = {
            id: "bootstrap-table-architecture",          // 指定表格ID
            toolbar: "toolbar-architecture",   // 指定工具栏ID
            formId: "form-architecture",
            url: prefix + "/list",
            createUrl: prefix + "/add/" + orgCode,
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "企业架构",
            columns: [{
                checkbox: true
            },
                {
                    align: 'center',
                    title: "序号",
                    width: 60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'nameCn',
                    title: '中文名称'
                },
                {
                    field: 'nameEn',
                    title: '英文名称'
                },
                {
                    field: 'parentName',
                    title: '上级名称',
                    formatter: function (value, row, index) {
                        return value === '' || value === null || value ===undefined ? topName : value;
                    }
                },
                {
                    field: 'type',
                    title: '类型'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.architectureId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="remove(\'' + row.architectureId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function add() {
        let orgName = encodeURI(encodeURI([[${orgName}]]))
        $.modal.open('修改企业架构', prefix + "/add/" + orgName + '/' + orgCode + '/' + $('#parentId').val(), null, 700);
    }

    function reset() {
        $('#parentId').val('0')
        $('#nameCn').val('')
        $.table.search('form-architecture', 'bootstrap-table-architecture')
        queryArchitectureTree();
    }

    function remove(architectureId) {
        $.modal.confirm("确定要删除该条企业架构吗？", function () {
            $.ajax({
                type: "GET",
                url: prefix + "/isParent/" + architectureId,
                async: false,
                error: function (request) {
                    $.modal.alertError("系统错误");
                },
                success: function (res) {
                    if (!res.data) {
                        $.ajax({
                            type: "GET",
                            url: prefix + "/remove/" + architectureId,
                            async: false,
                            error: function (request) {
                                $.modal.alertError("系统错误");
                            },
                            success: function (res) {
                                $.table.search('form-architecture', 'bootstrap-table-architecture')
                                $.modal.msgSuccess(res.msg);
                            }
                        });
                    } else {
                        $.modal.alertWarning("只能删除无下级节点！")
                    }
                }
            });
        });
    }

    $(function () {
        let panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({initClosed: panehHidden, west__size: 210});
        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
            var opt = {
                win: $('.ui-layout-center'),
                doc: $('.ui-layout-center')
            };
            $('#scroll-up').toTop(opt);
        }

        queryArchitectureTree();
    })

    function queryArchitectureTree() {
        let orgName = encodeURI(encodeURI([[${orgName}]]))
        var url = prefix + "/queryStructure/" + orgName + '/' + orgCode;
        var options = {
            url: url,
            expandLevel: 2,
            onClick: zOnClick
        };
        $.tree.init(options);

        /**
         * 节点点击事件
         * @param event
         * @param treeId
         * @param treeNode
         */
        function zOnClick(event, treeId, treeNode) {
            let id = treeNode.id;
            $('#parentId').val(id)
            $.table.search('form-architecture', 'bootstrap-table-architecture')
        };
    }

    $('#btnExpand').click(function () {
        $._tree.expandAll(true);
        $(this).hide();
        $('#btnCollapse').show();
    });

    $('#btnCollapse').click(function () {
        $._tree.expandAll(false);
        $(this).hide();
        $('#btnExpand').show();
    });

    $('#btnRefresh').click(function () {
        queryArchitectureTree();
        $('#btnExpand').hide();
        $('#btnCollapse').show();
    });
</script>
</body>
</html>