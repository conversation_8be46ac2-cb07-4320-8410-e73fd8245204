package com.lirong.organization.field.service;

import java.util.List;

import com.lirong.organization.field.domain.IdwOrgResearchField;

/**
 * 机构研究领域Service接口
 *
 * <AUTHOR>
 * @date 2023-03-24
 */
public interface IdwOrgResearchFieldService {
    /**
     * 查询机构研究领域
     *
     * @param fieldId 机构研究领域ID
     * @return 机构研究领域
     */
    public IdwOrgResearchField selectIdwOrgResearchFieldById(Long fieldId);

    /**
     * 查询机构研究领域列表
     *
     * @param idwOrgResearchField 机构研究领域
     * @return 机构研究领域集合
     */
    public List<IdwOrgResearchField> selectIdwOrgResearchFieldList(IdwOrgResearchField idwOrgResearchField);

    /**
     * 新增机构研究领域
     *
     * @param idwOrgResearchField 机构研究领域
     * @return 结果
     */
    public int insertIdwOrgResearchField(IdwOrgResearchField idwOrgResearchField);

    /**
     * 修改机构研究领域
     *
     * @param idwOrgResearchField 机构研究领域
     * @return 结果
     */
    public int updateIdwOrgResearchField(IdwOrgResearchField idwOrgResearchField);

    /**
     * 批量删除机构研究领域
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteIdwOrgResearchFieldByIds(String ids);
}
