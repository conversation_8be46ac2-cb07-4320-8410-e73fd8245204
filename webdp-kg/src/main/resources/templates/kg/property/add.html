<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('新增本体属性')" />
    <th:block th:include="include :: layout-latest-css"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-property">
                <div class="select-list">
                    <input type="hidden" name="tableName" id="tableName" th:value="${tableName}">
                    <ul>
                        <li>
                            <label>属性描述：</label>
                            <input type="text" name="columnName" id="columnName"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-property', 'bootstrap-table-property')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-property', 'bootstrap-table-property')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar-property" role="group">

        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table-property"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: layout-latest-js"/>
<script th:inline="javascript">
    let property = ctx + "kg/property";

    let ontologyId = [[${ontologyId}]]
    let oldColumnNames = [[${columnNames}]]

    $(function() {
        let options = {
            id: "bootstrap-table-property",          // 指定表格ID
            toolbar: "toolbar-property",   // 指定工具栏ID
            formId: "form-property",
            url: ctx + "tool/gen/db/tableColumns/list",
            rememberSelected: true,//翻页记住选择
            modalName: "本体属性",
            onCheck: onCheck,//选中行
            onUncheck: onUncheck,//取消选中行
            onCheckAll: onCheckAll,//全选
            onUncheckAll: onUncheckAll,//取消全选
            columns: [{
                field: 'state',
                checkbox: true,
                formatter: function (value, row, index) {
                    if((',' + oldColumnNames + ',').indexOf(',' + row.columnName + ',') != -1){
                        return { disabled : true}
                    } else {
                        return { disabled : false}
                    }
                }
            },
            {
                align: 'center',
                title: "序号",
                width: 60,
                formatter: function (value, row, index) {
                    return $.table.serialNumber(index);
                }
            },
            {
                field: 'columnName',
                title: '属性名称'
            },
            {
                field: 'columnComment',
                title: '属性描述'
            },
            {
                field: 'columnType',
                title: '属性类型'
            }]
        };
        $.table.init(options);
    });

    let columnNames = '';
    let columnComments = '';
    let columnTypes = '';
    //选中行
    function onCheck(row, $element) {
        if (columnNames == '') {
            columnNames = row.columnName;
            columnComments = row.columnComment;
            columnTypes = row.columnType;
        } else {
            columnNames += ',' + row.columnName;
            columnComments += ',' + row.columnComment;
            columnTypes += ',' + row.columnType;
        }
    }
    //取消选中行
    function onUncheck(row, $element) {
        columnNames = (',' + columnNames + ',').replace(',' + row.columnName + ',', ',').split(",,").join("");
        columnComments = (',' + columnComments + ',').replace(',' + row.columnComment + ',', ',').split(",,").join("");
        columnTypes = (',' + columnTypes + ',').replace(',' + row.columnType + ',', ',').split(",,").join("");
    }
    //全选
    function onCheckAll(rowsAfter, rowsBefore) {
        if (rowsBefore.length > 0){
            columnNames = (columnNames.substring(0, 1) == ',' ? '' : ',') + columnNames + (columnNames.substring(-1) == ',' ? '' : ',');
            columnComments = (columnComments.substring(0, 1) == ',' ? '' : ',') + columnComments + (columnComments.substring(-1) == ',' ? '' : ',');
            columnTypes = (columnTypes.substring(0, 1) == ',' ? '' : ',') + columnTypes + (columnNames.substring(-1) == ',' ? '' : ',');
            for (let i = 0; i < rowsBefore.length; i++) {
                let before = rowsBefore[i];
                columnNames = columnNames.replace(',' + before.columnName + ',', ',').split(",,").join("");
                columnComments = columnComments.replace(',' + before.columnComment + ',', ',').split(",,").join("");
                columnTypes = columnTypes.replace(',' + before.columnType + ',', ',').split(",,").join("");
            }
        }
        for (let i = 0; i < rowsAfter.length; i++) {
            let after = rowsAfter[i];
            columnNames += ',' + after.columnName;
            columnComments += ',' + after.columnComment;
            columnTypes += ',' + after.columnType;
        }
    }
    //取消全选
    function onUncheckAll(rowsAfter, rowsBefore) {
        columnNames = (columnNames.substring(0, 1) == ',' ? '' : ',') + columnNames + (columnNames.substring(-1) == ',' ? '' : ',');
        columnComments = (columnComments.substring(0, 1) == ',' ? '' : ',') + columnComments + (columnComments.substring(-1) == ',' ? '' : ',');
        columnTypes = (columnTypes.substring(0, 1) == ',' ? '' : ',') + columnTypes + (columnNames.substring(-1) == ',' ? '' : ',');
        for (let i = 0; i < rowsBefore.length; i++) {
            let before = rowsBefore[i];
            columnNames = columnNames.replace(',' + before.columnName + ',', ',').split(",,").join("");
            columnComments = columnComments.replace(',' + before.columnComment + ',', ',').split(",,").join("");
            columnTypes = columnTypes.replace(',' + before.columnType + ',', ',').split(",,").join("");
        }
    }
    
    function submitHandler() {
        if (columnNames == '' || columnNames ==','){
            $.modal.msgWarning('请选择属性！');
        } else {
            $.modal.loading("正在处理，请稍后...");
            $.ajax({
                type: "POST",
                url: property + "/add",
                data : {
                    'ontologyId': ontologyId,
                    'columnNames': columnNames,
                    'columnComments': columnComments,
                    'columnTypes': columnTypes
                },
                success: function(result) {
                    $.modal.closeLoading();
                    if (result.code == web_status.SUCCESS) {
                        window.parent.refreshPage()
                        $.modal.close();
                    } else {
                        $.modal.alertError(result.msg)
                    }
                }
            });
        }
    }
</script>
</body>
</html>