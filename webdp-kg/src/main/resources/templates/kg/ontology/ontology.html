<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('本体列表')" />
    <th:block th:include="include :: bootstrap-editable-css" />
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
    <style>
        .control-label, .control-value {
            padding-top: 7px;
            margin-bottom: 0;
            text-align: left;
        }
        .control-label {
            white-space: nowrap;
            font-weight: bold;
        }
        .color-tip {
            width: 15px;
            height: 15px;
            border-radius: 50%;
            margin-left: 2px;
            margin-right: 10px;
            display: block;
            float: left;
            background-color: #fff;
        }

        .content-div {
            padding: 0 28px;
        }
    </style>
</head>
<body class="gray-bg">
    <div class="ui-layout-west">
        <div class="box box-main">
            <div class="box-header">
                <div class="box-title">
                    <i class="fa icon-grid"></i> 本体
                </div>
                <div class="box-tools pull-right">
                    <button type="button" class="btn btn-box-tool" id="btnExpand" title="展开" style="display:none;"><i class="fa fa-chevron-up"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnCollapse" title="折叠"><i class="fa fa-chevron-down"></i></button>
                    <button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新本体"><i class="fa fa-refresh"></i></button>
                    <button type="button" class="btn btn-box-tool" onclick="add()" title="添加" shiro:hasPermission="kg:ontology:add">
                        <i class="fa fa-plus"></i>
                    </button>
                    <button type="button" class="btn btn-box-tool" onclick="edit()" title="修改" shiro:hasPermission="kg:ontology:edit">
                        <i class="fa fa-edit"></i>
                    </button>
                    <button type="button" class="btn btn-box-tool" onclick="remove()" title="删除" shiro:hasPermission="kg:ontology:remove">
                        <i class="fa fa-remove"></i>
                    </button>
                </div>
            </div>
            <div class="ui-layout-content">
                <div id="tree" class="ztree"></div>
            </div>
        </div>
    </div>

    <div class="ui-layout-center">
        <div class="wrapper wrapper-content animated fadeInRight" style="padding: 10px!important;">
            <div class="row">
                <div class="col-sm-12">
                    <div class="ibox float-e-margins" style="margin-bottom: 0!important; padding-bottom: 20px!important;">
                        <div class="ibox-title">
                            <span id="color" class="color-tip"></span>
                            <h5 id="entityName"></h5>
                            <div class="ibox-tools" id="tools" style="display: none;">
                                <a class="collapse-link" onclick="edit()">
                                    <i class="fa fa-edit"></i>
                                </a>
                                <a class="collapse-link" onclick="remove()">
                                    <i class="fa fa-remove"></i>
                                </a>
                            </div>
                        </div>
                        <div class="ibox-content">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="col-md-2 control-label">本体名称：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="ontologyName"></span>
                                    </div>
                                    <div class="col-md-2 control-label">执行状态：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="parentName"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="col-md-2 control-label">虚拟节点：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="virtualNode"></span>
                                    </div>
                                    <div class="col-md-2 control-label">需要创建：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="needCreate"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="col-md-2 control-label">创建人：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="createBy"></span>
                                    </div>
                                    <div class="col-md-2 control-label">创建时间：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="createTime"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="col-md-2 control-label">修改人：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="updateBy"></span>
                                    </div>
                                    <div class="col-md-2 control-label">修改时间：</div>
                                    <div class="col-md-4 control-value">
                                        <span id="updateTime"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="col-md-2 control-label">描述信息：</div>
                                    <div class="col-md-10 control-value">
                                        <span id="description"></span>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="content-div">
            <form id="form-property">
                <input name="ontologyId" id="ontologyId" type="hidden">
                <input name="name" id="name" type="hidden">
                <input name="type" value="kg" type="hidden">
            </form>
            <div class="row">
                <div class="ibox float-e-margins" style="margin-bottom: 0!important; padding-bottom: 0!important;">
                    <div class="ibox-title">
                        <h5>本体属性</h5>
                    </div>
                    <div class="ibox-content" style="padding: 5px 20px!important;">
                        <div class="btn-group-sm" id="toolbar-property" role="group">
                            <a class="btn btn-success" onclick="addProperty()" shiro:hasPermission="kg:property:add">
                                <i class="fa fa-plus"></i> 添加属性
                            </a>
                        </div>
                        <table id="bootstrap-table-property"></table>
                    </div>
                </div>
            </div>
        </div>
    </div>
<th:block th:include="include :: footer" />
<th:block th:include="include :: ztree-js"/>
<th:block th:include="include :: layout-latest-js"/>
<th:block th:include="include :: bootstrap-table-editable-js" />
<script th:inline="javascript">
    let editFlag = [[${@permission.hasPermi('kg:property:edit')}]];
    let removeFlag = [[${@permission.hasPermi('kg:property:remove')}]];
    let ontologyPrefix = ctx + "kg/ontology";
    let propertyPrefix = ctx + "kg/property";

    $(function () {
        let panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({initClosed: panehHidden, west__size: 250});
        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
            var opt = {
                win: $('.ui-layout-center'),
                doc: $('.ui-layout-center')
            };
            $("#ontologyClassifyEdit").attr('class', 'btn btn-primary disabled');
            $("#relevancyOrganization").attr('class', 'btn btn-warning disabled');
            queryOntologyTree()
            $('#scroll-up').toTop(opt);
        }
    })

    $(function() {
        let options = {
            id: "bootstrap-table-property",          // 指定表格ID
            toolbar: "toolbar-property",   // 指定工具栏ID
            formId: "form-property",
            url: propertyPrefix + "/list",
            createUrl: propertyPrefix + "/add",
            updateUrl: propertyPrefix + "/edit/{id}",
            removeUrl: propertyPrefix + "/remove",
            exportUrl: propertyPrefix + "/export",
            modalName: "本体属性",
            pageSize: 8,
            onEditableSave: onEditableSave,
            columns: [{
                checkbox: true
            },
            {
                align: 'center',
                title: "序号",
                width: 60,
                formatter: function (value, row, index) {
                    return $.table.serialNumber(index);
                }
            },
            {
                field: 'propName',
                title: '属性名称',
                editable: {
                    noEditFormatter: function (value, row, index) {
                        if (row.inherit === 1) {
                            return '<span class="badge badge-primary">继承</span>' + value
                        }
                        return false
                    }
                }
            },
            {
                field: 'columnName',
                title: '表字段名称'
            },
            {
                field: 'dataType',
                title: '数据类型'
            },
            {
                visible: editFlag !== 'hidden',
                title: '主键',
                align: 'center',
                formatter: function (value, row, index) {
                    let ontologyId = $('#ontologyId').val();
                    if (ontologyId != null && ontologyId !='' && ontologyId != row.ontologyId){
                        return  row.primaryKey == 1 ? '是' : '否';
                    } else {
                        return statusTools(row, 'primaryKey');
                    }
                }
            },
            {
                visible: editFlag !== 'hidden',
                title: '节点名称',
                align: 'center',
                formatter: function (value, row, index) {
                    let ontologyId = $('#ontologyId').val();
                    if (ontologyId != null && ontologyId !='' && ontologyId != row.ontologyId){
                        return  row.asName == 1 ? '是' : '否';
                    } else {
                        return statusTools(row, 'asName');
                    }
                }
            },
            {
                visible: editFlag !== 'hidden',
                title: '显示',
                align: 'center',
                formatter: function (value, row, index) {
                    let ontologyId = $('#ontologyId').val();
                    if (ontologyId != null && ontologyId !='' && ontologyId != row.ontologyId){
                        return  row.show == 1 ? '是' : '否';
                    } else {
                        return statusTools(row, 'show');
                    }
                }
            },
            {
                title: '操作',
                align: 'center',
                formatter: function(value, row, index) {
                    let actions = [];
                    if (row.inherit === 0) {
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="propertyRemove(\'' + row.propId + '\',\'' + row.ontologyId + '\',\'' + row.columnName + '\')"><i class="fa fa-remove"></i>删除</a>');
                    }
                    return actions.join('');
                }
            }]
        };
        $.table.init(options);
    });

    //新增本体属性
    function addProperty() {
        let ontologyId = $('#ontologyId').val();
        if (ontologyId !== null && ontologyId !== '' && ontologyId !== undefined){
            $.modal.open('新增属性', propertyPrefix + "/add/" + ontologyId);
        } else {
            $.modal.msgWarning('请先选择本体！');
        }
    }

    //删除本体属性
    function propertyRemove(propId, ontologyId, columnName) {
        $.modal.confirm("确认删除选中本体属性吗？", function() {
            $.ajax({
                type: "GET",
                url: ontologyPrefix + "/syncField/" + ontologyId,
                async: false,
                success: function(result) {
                    if (result.code == web_status.SUCCESS) {
                        if (result.msg != columnName){
                            $.ajax({
                                type: "GET",
                                url: propertyPrefix + "/remove/" + propId,
                                async: false,
                                success: function(result) {
                                    if (result.code == web_status.SUCCESS) {
                                        $.modal.msgSuccess("删除成功！");
                                        $.table.search('form-property', 'bootstrap-table-property');
                                    } else {
                                        $.modal.msgWarning(result.msg);
                                    }
                                }
                            });
                        } else {
                            $.modal.msgWarning('不能删除同步字段！');
                        }
                    } else {
                        $.modal.msgWarning(result.msg);
                    }
                }
            });
        })
    }

    function statusTools(row, type) {
        let value;
        if (type === 'primaryKey'){
            value = row.primaryKey
        }
        if (type === 'asName'){
            value = row.asName
        }
        if (type === 'show'){
            value = row.show
        }
        if (row.inherit === 1) {
            return value === 1 ? '是' : '否'
        } else {
            if (value === 0) {
                return '<i class=\"fa fa-toggle-off text-info fa-2x\" onclick="enable(\'' + row.propId + '\',\''+ type + '\')"></i> ';
            } else {
                return '<i class=\"fa fa-toggle-on text-info fa-2x\" onclick="disable(\'' + row.propId + '\',\''+ type + '\')"></i> ';
            }
        }
    }

    //停用
    function disable(propId, type) {
        let title;
        let data;
        if (type == 'primaryKey'){
            title = '不作为主键'
            data = { 'propId': propId, 'primaryKey': 0 }
        }
        if (type == 'asName'){
            title = '不作为节点名称'
            data = { 'propId': propId, 'asName': 0 }
        }
        if (type == 'show'){
            title = '不显示'
            data = { 'propId': propId, 'show': 0 }
        }
        $.modal.confirm("确认" + title + "吗？", function() {
            $.operate.post(propertyPrefix + "/edit", data);
        })
    }

    //启用
    function enable(propId, type) {
        let title;
        let data;
        if (type == 'primaryKey'){
            title = '设为主键'
            data = { 'propId': propId, 'primaryKey': 1 }
        }
        if (type == 'asName'){
            title = '作为节点名称'
            data = { 'propId': propId, 'asName': 1 }
        }
        if (type == 'show'){
            title = '显示'
            data = { 'propId': propId, 'show': 1 }
        }
        $.modal.confirm("确认" + title + "吗？", function() {
            if (type == 'asName'){
                //校验是否已有节点名称
                $.ajax({
                    type: "GET",
                    url: propertyPrefix + "/verifyIsExistNameOntologyId/" + $('#ontologyId').val(),
                    async: false,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            if (!result.data){
                                $.operate.post(propertyPrefix + "/edit", data);
                            } else {
                                $.modal.alertWarning('已有节点名称！');
                            }
                        } else {
                            $.modal.alertWarning(result.msg);
                        }
                    }
                });
            } else {
                $.operate.post(propertyPrefix + "/edit", data);
            }
        })
    }

    function onEditableSave (field, row, rowIndex, oldValue, $el) {
        $.operate.post(propertyPrefix + "/edit", { 'propId': row.propId, 'propName': row[field] });
    }

    //新增本体
    function add() {
        let pid = $('#ontologyId').val()
        if (pid != null && pid != '' && pid != undefined){
            $.modal.open('添加本体', ontologyPrefix + "/add/" + pid);
        } else {
            $.modal.open('添加本体', ontologyPrefix + "/add");
        }
    }
    //修改本体
    function edit() {
        let ontologyId = $('#ontologyId').val();
        if (ontologyId != null && ontologyId != '' && ontologyId != undefined){
            $.modal.open('修改本体', ontologyPrefix + "/edit/" + ontologyId);
        } else {
            $.modal.msgWarning('请先选择本体！')
        }
    }
    //修改本体关联机构
    function editOrg(ontologyId) {
        $.modal.open('修改本体', ontologyPrefix + "/edit/" + ontologyId + '/true', null, 500);
    }
    //删除本体
    function remove() {
        let ontologyId = $('#ontologyId').val();
        if (ontologyId != null && ontologyId != '' && ontologyId != undefined){
            $.modal.confirm("确定删除选择本体吗？", function() {
                //根据ID校验是否存在子集
                $.ajax({
                    type: "GET",
                    url: propertyPrefix + "/verifyIsExist/" + ontologyId,
                    async: false,
                    success: function(isExist) {
                        if (!isExist) {
                            $.ajax({
                                type: "GET",
                                url: ontologyPrefix + "/" + ontologyId,
                                async: false,
                                success: function(result) {
                                    if (result.code == web_status.SUCCESS) {
                                        $.modal.msgSuccess("删除成功！");
                                        refreshPage();
                                    } else {
                                        $.modal.msgWarning(result.msg)
                                    }
                                }
                            });
                        } else {
                            $.modal.msgWarning('请先删除本体属性！')
                        }
                    }
                });
            });
        } else {
            $.modal.msgWarning('请先选择主题！')
        }
    }

    //加载本体树
    function queryOntologyTree() {
        var url = ontologyPrefix + "/ontologyTree";
        var options = {
            url: url,
            view: {
                showIcon : false,
                addDiyDom: addDiyDom
            },
            expandLevel: 3,
            onClick: onClick,
            callBack: onInit    // 加载完毕后的函数
        };
        $.tree.init(options);

        function onInit(tree) {
            let nodes = tree.getNodes();
            if (nodes.length > 0) {
                tree.selectNode(nodes[0])
                onClick(null, null, nodes[0])
            }
        }

        /**
         * 节点点击事件
         */
        function onClick(event, treeId, treeNode) {
            let code = treeNode.code;
            if (code != null){
                $('#ontologyId').val(code)
                $.ajax({
                    type: "GET",
                    url: ontologyPrefix + "/select/" + code,
                    async: false,
                    success: function(result) {
                        if (result.code == web_status.SUCCESS) {
                            let ontology = result.data;
                            $('#entityName').html(ontology.name);
                            $('#color').css('background-color', ontology.color);
                            $('#ontologyName').html(ontology.name)
                            $('#parentName').html(ontology.parentName)
                            $('#virtualNode').html(ontology.virtual === 1 ? '是' : '否')
                            $('#needCreate').html(ontology.needCreate === 1 ? '是' : '否')
                            $('#createBy').html(ontology.createBy)
                            $('#createTime').html(ontology.createTime)
                            $('#updateBy').html(ontology.updateBy)
                            $('#updateTime').html(ontology.updateTime)
                            $('#description').html(ontology.description)
                            $('#tools').css('display', 'block')
                        } else {
                            $.modal.msgWarning(result.msg)
                        }
                    }
                });
            } else {
                $('#ontologyId').val(0)
                $('#entityName').html("");
                $('#color').css('background-color', "");
                $('#ontologyName').html("")
                $('#parentName').html("")
                $('#virtualNode').html("")
                $('#needCreate').html("")
                $('#createBy').html("")
                $('#createTime').html("")
                $('#updateBy').html("")
                $('#updateTime').html("")
                $('#description').html("")
                $('#tools').css('display', 'block')
            }
            $("#ontologyClassifyEdit").attr('class', 'btn btn-primary');
            $("#relevancyOrganization").attr('class', 'btn btn-warning');
            $.table.search('form-property', 'bootstrap-table-property')
        }

        //在节点名称前拼接色块
        function addDiyDom(treeId, treeNode) {
            var aObj = $("#" + treeNode.tId + '_a');
            let  colorLump = '<span style="padding: 0px 7.5px; border-radius: 50%; margin-left: 2px; background-color: ' + treeNode.color + ';"></span>'
            aObj.before(colorLump);
            let btn = $("#diyBtn_"+treeNode.id);
            if (btn) btn.bind("click", function(){alert("diy Button for " + treeNode.name);});
        }
    }

    $('#btnRefresh').click(function () {
        queryOntologyTree();
    });

    //展开所有节点
    $('#btnExpand').click(function () {
        var treeObj = $.fn.zTree.getZTreeObj("tree");
        treeObj.expandAll(true);
        $(this).hide();
        $('#btnCollapse').show();
    });

    //收缩所有节点
    $('#btnCollapse').click(function () {
        var treeObj = $.fn.zTree.getZTreeObj("tree");
        treeObj.expandAll(false);
        $(this).hide();
        $('#btnExpand').show();
    });

    function refreshPage() {
        $.table.search('form-property', 'bootstrap-table-property')
        queryOntologyTree()
    }
</script>
</body>
</html>