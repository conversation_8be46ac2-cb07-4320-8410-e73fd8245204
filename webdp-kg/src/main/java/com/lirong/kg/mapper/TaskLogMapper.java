package com.lirong.kg.mapper;

import java.util.List;

import com.lirong.kg.domain.TaskLog;

/**
 * 任务执行日志Mapper接口
 *
 * <AUTHOR>
 * @date 2022-03-21
 */
public interface TaskLogMapper {

    /**
     * 查询任务执行日志
     *
     * @param logId 任务执行日志ID
     * @return 任务执行日志
     */
    public TaskLog selectTaskLogById(Long logId);

    /**
     * 查询任务执行日志列表
     *
     * @param taskLog 任务执行日志
     * @return 任务执行日志集合
     */
    public List<TaskLog> selectTaskLogList(TaskLog taskLog);

    /**
     * 新增任务执行日志
     *
     * @param taskLog 任务执行日志
     * @return 结果
     */
    public int insertTaskLog(TaskLog taskLog);

    /**
     * 修改任务执行日志
     *
     * @param taskLog 任务执行日志
     * @return 结果
     */
    public int updateTaskLog(TaskLog taskLog);

    /**
     * 删除任务执行日志
     *
     * @param logId 任务执行日志ID
     * @return 结果
     */
    public int deleteTaskLogById(Long logId);

    /**
     * 批量删除任务执行日志
     *
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteTaskLogByIds(String[] logIds);

    /**
     *
     * @param taskIds
     * @return
     */
    public int deleteLogByTaskIds(String[] taskIds);
}
