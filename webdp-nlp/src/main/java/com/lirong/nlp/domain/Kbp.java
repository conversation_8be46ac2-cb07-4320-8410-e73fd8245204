package com.lirong.nlp.domain;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/5/19
 * @description 描述信息
 */
public class Kbp implements Serializable {

    // 起始对象
    private String subject;
    private Integer[] subjectSpan = new Integer[2];

    // 关系
    private String relation;
    private Integer[] relationSpan = new Integer[2];

    // 目标, ,
    private String object;
    private Integer[] objectSpan = new Integer[2];


    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Integer[] getSubjectSpan() {
        return subjectSpan;
    }

    public void setSubjectSpan(Integer[] subjectSpan) {
        this.subjectSpan = subjectSpan;
    }

    public String getObject() {
        return object;
    }

    public void setObject(String object) {
        this.object = object;
    }

    public Integer[] getObjectSpan() {
        return objectSpan;
    }

    public void setObjectSpan(Integer[] objectSpan) {
        this.objectSpan = objectSpan;
    }

    public String getRelation() {
        return relation;
    }

    public void setRelation(String relation) {
        this.relation = relation;
    }

    public Integer[] getRelationSpan() {
        return relationSpan;
    }

    public void setRelationSpan(Integer[] relationSpan) {
        this.relationSpan = relationSpan;
    }

}
