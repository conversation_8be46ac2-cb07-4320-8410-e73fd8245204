import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.fasterxml.jackson.databind.ObjectMapper;
import net.coobird.thumbnailator.Thumbnails;
import org.icepdf.core.exceptions.PDFException;
import org.icepdf.core.exceptions.PDFSecurityException;
import org.icepdf.core.pobjects.Document;
import org.icepdf.core.util.GraphicsRenderingHints;
import org.junit.Test;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.awt.image.RenderedImage;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class test {

    /*@Test
    public void test() throws IOException {
        FileInputStream fileInputStream = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\video (1).png"));
        FileInputStream fileInputStream1 = new FileInputStream(new File("C:\\Users\\<USER>\\Desktop\\video.png"));
        String md5Hex = DigestUtils.md5Hex(fileInputStream);
        String md5Hex1 = DigestUtils.md5Hex(fileInputStream1);
        fileInputStream.close();
        fileInputStream1.close();
        System.out.println(md5Hex);
        System.out.println(md5Hex1);
    }*/

    @Test
    public static void main(String[] args) {
        String s = englishTranslate("Boeing Executive Vice President and CFO West to Speak at Goldman Sachs Industrials and Materials Conference May 11");
        JSONObject jo =  JSONObject.parseObject(s);
        String content = ((Map<String,String>)  ((JSONObject) jo.getJSONArray("translation").get(0)).getJSONArray("translated").get(0)).get("text");
        System.out.println(content);
        /*String s = "";
        try {
            pdfToImage();
        } catch (PDFSecurityException e) {
            e.printStackTrace();
        } catch (PDFException e) {
            e.printStackTrace();
        } catch (IOException exception) {
            exception.printStackTrace();
        } catch (InterruptedException e) {
            e.printStackTrace();
        }*/

    }

    public static void pdfToImage() throws PDFSecurityException, PDFException, IOException, InterruptedException {
        String filePath = "C:\\Users\\<USER>\\Desktop\\空军年鉴June2020_Fullissue5 (1).pdf";//要转图片的pdf地址
        File pdfFile = new File(filePath);
        Document document = new Document();
        document.setFile(filePath);
        float scale = 1f;//缩放比例
        float rotation = 0f;//旋转角度

        for (int i = 0; i < document.getNumberOfPages(); i++) {
            BufferedImage image = (BufferedImage)
                    document.getPageImage(i, GraphicsRenderingHints.SCREEN, org.icepdf.core.pobjects.Page.BOUNDARY_CROPBOX, rotation, scale);
            RenderedImage rendImage = image;
            try {
                String pdfFileName = pdfFile.getName();
                File file = new File(pdfFile.getParent() + pdfFileName.replace(".pdf", "") + ".jpg");//生成的图片地址
                ImageIO.write(rendImage, "jpg", file);
                Thumbnails.of(file)//这一段代码是用来把图片压缩的，不需要可以直接删掉
                        .scale(1f) //图片大小（长宽）压缩比例 从0-1，1表示原图
                        .outputQuality(0.5f) //图片质量压缩比例 从0-1，越接近1质量越好
                        .toOutputStream(new FileOutputStream("e:/iecPDF_cp" + i + ".jpg"));//压缩后的图片地址
//                file.delete(); //这里是用来删除没有压缩的图片，
            } catch (IOException e) {
                e.printStackTrace();
            }
            image.flush();
        }
        document.dispose();
    }


    private static final String URL = "http://fay.transn.net/mt-api/v1/common/en-zh/translate";

    /**
     * 传神英文翻译
     *
     * @param text 内容
     * @return String
     */
    public static String englishTranslate(String text) {
        String result = "";
        BufferedReader in = null;
        PrintWriter out = null;
        try {
            //请求头
            Map<String, String> header = new HashMap<>();
            header.put("content-type", " application/json");
            //请求体
            Map<String, String> map = new HashMap<>();
            map.put("text", text);
            ObjectMapper mapper = new ObjectMapper();
            String body = mapper.writeValueAsString(map);
            // 设置 url
            URL realUrl = new URL(URL);
            HttpURLConnection connection = (HttpURLConnection) realUrl.openConnection();
            // 设置 header
            for (String key : header.keySet()) {
                connection.setRequestProperty(key, header.get(key));
            }
            // 设置请求 body
            connection.setDoOutput(true);
            connection.setDoInput(true);

            try {
                out = new PrintWriter(connection.getOutputStream());
                // 保存body
                out.print(body);
                // 发送body
                out.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
            try {
                // 获取响应body
                in = new BufferedReader(new InputStreamReader(connection.getInputStream(), "UTF-8"));
                String line;
                while ((line = in.readLine()) != null) {
                    result += line;
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
            //return null;
        }
        return result;
    }

}
