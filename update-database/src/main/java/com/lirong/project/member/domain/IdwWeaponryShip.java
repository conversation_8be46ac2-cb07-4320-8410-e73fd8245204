package com.lirong.project.member.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 舰船迁移对象 idw_weaponry_ship
 *
 * <AUTHOR>
 * @date 2023-02-08
 */
public class IdwWeaponryShip extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 装备编码
     */
    private String weaponryCode;

    /**
     * 中文名称
     */
    @Excel(name = "中文名称")
    private String nameCn;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String nameEn;

    /**
     * 舰船ID
     */
    @Excel(name = "舰船ID")
    private Long shipId;

    /**
     * 舰船名称
     */
    @Excel(name = "舰船名称")
    private String shipName;

    public void setWeaponryCode(String weaponryCode) {
        this.weaponryCode = weaponryCode;
    }

    public String getWeaponryCode() {
        return weaponryCode;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setShipId(Long shipId) {
        this.shipId = shipId;
    }

    public Long getShipId() {
        return shipId;
    }

    public void setShipName(String shipName) {
        this.shipName = shipName;
    }

    public String getShipName() {
        return shipName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("weaponryCode", getWeaponryCode())
                .append("nameCn", getNameCn())
                .append("nameEn", getNameEn())
                .append("shipId", getShipId())
                .append("shipName", getShipName())
                .toString();
    }
}
