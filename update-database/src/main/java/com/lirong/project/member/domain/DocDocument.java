package com.lirong.project.member.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * 文献资料对象 doc_document
 *
 * <AUTHOR>
 * @date 2023-02-20
 */
public class DocDocument extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 编号
     */
    private Long id;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 译名
     */
    @Excel(name = "译名")
    private String translatedTitle;

    /**
     * 发布机构
     */
    @Excel(name = "发布机构")
    private String publisher;

    /**
     * 发布日期
     */
    @Excel(name = "发布日期")
    private String publishDate;

    /**
     * 作者
     */
    @Excel(name = "作者")
    private String authors;

    /**
     * 缩略图
     */
    @Excel(name = "缩略图")
    private String thumbnail;

    /**
     * 摘要
     */
    @Excel(name = "摘要")
    private String summary;

    /**
     * 文献类型
     */
    @Excel(name = "文献类型")
    private String type;

    /**
     * 领域
     */
    @Excel(name = "领域")
    private String area;

    /**
     * 分类
     */
    @Excel(name = "分类")
    private String category;

    /**
     * 主题
     */
    @Excel(name = "主题")
    private String topics;

    /**
     * 资源内容
     */
    @Excel(name = "资源内容")
    private String content;

    /**
     * 文件地址
     */
    @Excel(name = "文件地址")
    private String filePath;

    /**
     * 下载次数
     */
    @Excel(name = "下载次数")
    private Integer download;

    /**
     * 价格
     */
    @Excel(name = "价格")
    private BigDecimal price;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String url;

    /**
     * 采集时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "采集时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date collectTime;

    /**
     * 发布状态：1-已发布，0-未发布
     */
    @Excel(name = "发布状态：1-已发布，0-未发布")
    private Integer post;

    /**
     * 文件页数
     */
    @Excel(name = "文件页数")
    private Integer page;

    /**
     * 创建者
     */
    @Excel(name = "创建者")
    private String creator;

    /**
     * 更新者
     */
    @Excel(name = "更新者")
    private String updater;

    /**
     * 是否删除
     */
    @Excel(name = "是否删除")
    private Integer deleted;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getTitle() {
        return title;
    }

    public void setTranslatedTitle(String translatedTitle) {
        this.translatedTitle = translatedTitle;
    }

    public String getTranslatedTitle() {
        return translatedTitle;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublishDate(String publishDate) {
        this.publishDate = publishDate;
    }

    public String getPublishDate() {
        return publishDate;
    }

    public void setAuthors(String authors) {
        this.authors = authors;
    }

    public String getAuthors() {
        return authors;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setSummary(String summary) {
        this.summary = summary;
    }

    public String getSummary() {
        return summary;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getType() {
        return type;
    }

    public void setArea(String area) {
        this.area = area;
    }

    public String getArea() {
        return area;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategory() {
        return category;
    }

    public void setTopics(String topics) {
        this.topics = topics;
    }

    public String getTopics() {
        return topics;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getContent() {
        return content;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setDownload(Integer download) {
        this.download = download;
    }

    public Integer getDownload() {
        return download;
    }

    public void setPrice(BigDecimal price) {
        this.price = price;
    }

    public BigDecimal getPrice() {
        return price;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getUrl() {
        return url;
    }

    public void setCollectTime(Date collectTime) {
        this.collectTime = collectTime;
    }

    public Date getCollectTime() {
        return collectTime;
    }

    public void setPost(Integer post) {
        this.post = post;
    }

    public Integer getPost() {
        return post;
    }

    public void setPage(Integer page) {
        this.page = page;
    }

    public Integer getPage() {
        return page;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public String getCreator() {
        return creator;
    }

    public void setUpdater(String updater) {
        this.updater = updater;
    }

    public String getUpdater() {
        return updater;
    }

    public void setDeleted(Integer deleted) {
        this.deleted = deleted;
    }

    public Integer getDeleted() {
        return deleted;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("title", getTitle())
                .append("translatedTitle", getTranslatedTitle())
                .append("publisher", getPublisher())
                .append("publishDate", getPublishDate())
                .append("authors", getAuthors())
                .append("thumbnail", getThumbnail())
                .append("summary", getSummary())
                .append("type", getType())
                .append("area", getArea())
                .append("category", getCategory())
                .append("topics", getTopics())
                .append("content", getContent())
                .append("filePath", getFilePath())
                .append("download", getDownload())
                .append("price", getPrice())
                .append("url", getUrl())
                .append("collectTime", getCollectTime())
                .append("post", getPost())
                .append("page", getPage())
                .append("creator", getCreator())
                .append("createTime", getCreateTime())
                .append("updater", getUpdater())
                .append("updateTime", getUpdateTime())
                .append("deleted", getDeleted())
                .toString();
    }
}