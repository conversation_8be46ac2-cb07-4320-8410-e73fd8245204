package com.lirong.project.member.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.lirong.common.utils.StringUtils;
import com.lirong.project.member.utils.translate.TransApi;

import java.util.concurrent.atomic.AtomicLong;

public class BaiDuTranslateUtils {

    private static final String APP_ID = "20220526001229921";
    private static final String SECURITY_KEY = "W22QTIEwqOW5uktcltV1";
    private static TransApi api = new TransApi(APP_ID, SECURITY_KEY);
    private static AtomicLong expend = new AtomicLong();

    /**
     * 错误码	含义	解决方法
     * 52000	成功
     * 52001	请求超时	重试
     * 52002	系统错误	重试
     * 52003	未授权用户	检查您的 appid 是否正确，或者服务是否开通
     * 54000	必填参数为空	检查是否少传参数
     * 54001	签名错误	请检查您的签名生成方法
     * 54003	访问频率受限	请降低您的调用频率
     * 54004	账户余额不足	请前往管理控制台为账户充值
     * 54005	长query请求频繁	请降低长query的发送频率，3s后再试
     * 58000	客户端IP非法	检查个人资料里填写的 IP地址 是否正确
     * 可前往管理控制平台修改
     * IP限制，IP可留空
     * 58001	译文语言方向不支持	检查译文语言是否在语言列表里
     * 58002	服务当前已关闭	请前往管理控制台开启服务
     * 90107	认证未通过或未生效	请前往我的认证查看认证进度
     * @param text
     * @return
     */
    public static String translate(String text) {
        if (StringUtils.isBlank(text)){
            return text;
        }
        int tryTimes = 0;
        long current = System.currentTimeMillis();
        JSONObject object = JSON.parseObject(api.getTransResult(text, "auto", "zh"));
        tryTimes++;
        StringBuilder builder = new StringBuilder();

        JSONArray array = object.getJSONArray("trans_result");
        if (array != null) {
            for (int i = 0; i < array.size(); i++) {
                JSONObject obj = array.getJSONObject(i);
                builder.append(obj.get("dst"));
            }
        } else {
            System.err.println(object.toJSONString());
            // 由于调用过于频繁失败时等待1秒钟重新调用
            if ("54003".equals(object.get("error_code")) && tryTimes <= 3) {
                try {
                    Thread.sleep(1000);
                    tryTimes++;
                    return translate(text);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
            // 判断是否翻译
            if (!text.isEmpty() && ("52001".equals(object.get("error_code")) || "52002".equals(object.get("error_code"))) && tryTimes <= 3) {
                try {
                    Thread.sleep(1000);
                    tryTimes++;
                    return translate(text);
                } catch (InterruptedException e) {
                    e.printStackTrace();
                }
            }
        }

        long expendTime = (System.currentTimeMillis() - current);
        expend.set(expendTime);

        return builder.toString();
    }
}
