<head>
    <th:block th:include="include :: header('修改卫星型号')" />
    <th:block th:include="include :: select2-css" />
</head>
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-model-edit" th:object="${satelliteModel}">
            <input name="modelId" th:field="*{modelId}" type="hidden">
            <input name="weaponryCode" th:field="*{weaponryCode}" type="hidden">
            <div class="row">
                <div class="col-sm-9">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">装备分类：</label>
                                <div class="col-sm-8">
                                    <input id="treeClassificationCode" name="classificationCode" type="hidden" th:field="*{classificationCode}"/>
                                    <input name="classificationName" readonly="true" th:field="*{classificationName}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">国家/地区：</label>
                                <div class="col-sm-8">
                                    <select name="country" id="country" class="form-control" th:with="type=${@dict.getType('sys_country')}" required>
                                        <option value="" style="color: #b6b6b6" disabled selected>选择国家/地区</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{country}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">卫星中文名称：</label>
                                <div class="col-sm-8">
                                    <div class="input-group">
                                        <input type="hidden" name="satelliteCode" id="satelliteCode" th:field="*{satelliteCode}">
                                        <input type="text" class="form-control" name="satelliteNameCn" th:field="*{satelliteNameCn}" id="satelliteNameCn" placeholder="可通过卫星名称搜索" required>
                                        <div class="input-group-btn">
                                            <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">
                                                <span class="caret"></span>
                                            </button>
                                            <ul class="dropdown-menu dropdown-menu-right" role="menu">
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">卫星英文名称：</label>
                                <div class="col-sm-8">
                                    <input name="satelliteNameEn" id="satelliteNameEn" th:field="*{satelliteNameEn}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">型号中文名称：</label>
                                <div class="col-sm-8">
                                    <input name="satelliteModelNameCn" th:field="*{satelliteModelNameCn}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">型号英文名称：</label>
                                <div class="col-sm-8">
                                    <input name="satelliteModelNameEn" th:field="*{satelliteModelNameEn}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">使用状态：</label>
                                <div class="col-sm-8">
                                    <input name="status" th:field="*{status}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">发射：</label>
                                <div class="col-sm-8">
                                    <input name="launch" th:field="*{launch}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">轨道：</label>
                                <div class="col-sm-8">
                                    <input name="orbit" th:field="*{orbit}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-3">
                    <div class="form-group text-center">
                        <input type="hidden" name="picture" id="picture" th:field="*{picture}">
                        <p class="user-info-head" onclick="picture()">
                            <img class="img-lg" id="pictureUrl" th:src="*{picture}" th:onerror="'this.src=\'' + @{'/img/default_satellite.png'} + '\''">
                        </p>
                        <p><input type="file" id="pictureInput" style="display: none;"></p>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">设计寿命：</label>
                        <div class="col-sm-8">
                            <input name="designLife" th:field="*{designLife}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">作用：</label>
                        <div class="col-sm-8">
                            <input name="applications" th:field="*{applications}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">质量：</label>
                        <div class="col-sm-8">
                            <input name="mass" th:field="*{mass}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">功率：</label>
                        <div class="col-sm-8">
                            <input name="power" th:field="*{power}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">传感器：</label>
                        <div class="col-sm-8">
                            <input name="sensors" th:field="*{sensors}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">花费：</label>
                        <div class="col-sm-8">
                            <input name="cost" th:field="*{cost}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">航天器：</label>
                        <div class="col-sm-8">
                            <input name="spacecraft" th:field="*{spacecraft}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">姿态控制：</label>
                        <div class="col-sm-8">
                            <input name="attitudeControl" th:field="*{attitudeControl}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">指向经度：</label>
                        <div class="col-sm-8">
                            <input name="propulsion" th:field="*{propulsion}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">衰变日期：</label>
                        <div class="col-sm-8">
                            <input name="decayDate" th:field="*{decayDate}" class="form-control" type="text">
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">服役状态：</label>
                        <div class="col-sm-8">
                            <select name="serviceStatus" id="serviceStatus" class="form-control" th:with="type=${@dict.getType('service_status')}" required>
                                <option value="" style="color: #b6b6b6" disabled selected>选择服役状态</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}" th:field="*{serviceStatus}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">所属军兵种：</label>
                        <div class="col-sm-8">
                            <select name="troopsCategory" id="troopsCategory" class="form-control" th:with="type=${@dict.getType('sys_troops_categories')}" multiple>
                                <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label">是否显示前台：</label>
                        <div class="col-sm-8">
                            <div class="radio check-box">
                                <label><input type="radio" value="1" name="showHome" id="showHomeTrue"> <i></i> 是</label>
                            </div>
                            <div class="radio check-box">
                                <label><input type="radio" checked="" value="0" name="showHome" id="showHomeFalse"> <i></i>否</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-3">
                    <div class="form-group">
                        <label class="col-sm-4 control-label is-required">排序：</label>
                        <div class="col-sm-8">
                            <input name="orderNum" th:field="*{orderNum}" class="form-control" type="text" digits="true" required>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" id="contractorDiv">
                <div class="col-sm-2">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">承包商：</label>
                        <div class="col-sm-1">
                            <a class="btn btn-white btn-bitbucket" onclick="addContractor()">
                                <i class="fa fa-plus"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-1 control-label">研制背景：</label>
                <div class="col-sm-11">
                    <textarea name="developmentBackground" class="form-control">[[*{developmentBackground}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">配置：</label>
                <div class="col-sm-11">
                    <textarea name="configuration" class="form-control">[[*{configuration}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">有效载荷：</label>
                <div class="col-sm-11">
                    <textarea name="payload" class="form-control">[[*{payload}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">通信：</label>
                <div class="col-sm-11">
                    <textarea name="communications" class="form-control">[[*{communications}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">中文简介：</label>
                <div class="col-sm-11">
                    <textarea name="profileCn" class="form-control">[[*{profileCn}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label">英文简介：</label>
                <div class="col-sm-11">
                    <textarea name="profileEn" class="form-control">[[*{profileEn}]]</textarea>
                </div>
            </div>

            <div class="form-group">
                <label class="col-sm-1 control-label">备注：</label>
                <div class="col-sm-11">
                    <textarea name="notes" class="form-control">[[*{notes}]]</textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label is-required">数据来源：</label>
                <div class="col-sm-11">
                    <input name="source" th:field="*{source}" class="form-control" required>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script th:inline="javascript">
        let prefix = ctx + "weaponry/model";
        $("#form-model-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-model-edit').serialize());
            }
        }

        //赋值所属军兵种 troopsCategory
        let troopsCategory = [[${satelliteModel.troopsCategory}]]
        if (troopsCategory){
            $('#troopsCategory').val(troopsCategory.split(','));
        }

        let showHome = [[${satelliteModel.showHome}]]
        if (showHome == 1){
            $("#showHomeFalse").iCheck('uncheck');
            $("#showHomeTrue").iCheck('check');
        }

        let contractorIndex = 0;
        //回显承包商
        let weaponryContractorList = [[${weaponryContractor}]]
        if (weaponryContractorList != null && weaponryContractorList != '' && weaponryContractorList != undefined && weaponryContractorList.length > 0){
            for (let i = 0; i < weaponryContractorList.length; i++) {
                let weaponryContractor = weaponryContractorList[i];
                contractorIndex++;
                let inventor = '<div class="col-sm-3" id="contractorTemplate' + contractorIndex + '">\n' +
                    '                <div class="form-group">\n' +
                    '                    <div class="col-sm-10">\n' +
                    '                        <div class="input-group">\n' +
                    '                            <input type="hidden" id="contractorId' + contractorIndex + '" name="contractorId" value="' + weaponryContractor.contractorId + '">\n' +
                    '                            <input type="hidden" id="contractorCode' + contractorIndex + '" name="contractorCode" value="' + weaponryContractor.orgCode + '">\n' +
                    '                            <input type="hidden" id="contractorNameEn' + contractorIndex + '" name="contractorNameEn" value="' + weaponryContractor.orgNameEn + '">\n' +
                    '                            <input type="hidden" id="isUpdateContractor' + contractorIndex + '" name="isUpdateContractor" value="false">\n' +
                    '                            <input type="text" class="form-control" id="contractorNameCn' +contractorIndex + '" placeholder="可通过承包商名称搜索" name="contractorNameCn" value="' + weaponryContractor.orgNameCn + '">\n' +
                    '                            <div class="input-group-btn">\n' +
                    '                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">\n' +
                    '                                    <span class="caret"></span>\n' +
                    '                                </button>\n' +
                    '                                <ul class="dropdown-menu dropdown-menu-right" role="menu">\n' +
                    '                                </ul>\n' +
                    '                            </div>\n' +
                    '                        </div>\n' +
                    '                    </div>\n' +
                    '                    <div class="col-sm-1">\n' +
                    '                        <a class="btn btn-white btn-bitbucket" onclick="removeContractorLabel(\'' + contractorIndex + '\')">\n' +
                    '                            <i class="fa fa-remove"></i>\n' +
                    '                        </a>\n' +
                    '                    </div>\n' +
                    '                </div>\n' +
                    '            </div>';
                $("#contractorDiv").append(inventor)
                initContractorBsSuggest('contractorNameCn' + contractorIndex)
            }
        }
        function addContractor() {
            contractorIndex++;
            let inventor = '<div class="col-sm-3" id="contractorTemplate' + contractorIndex + '">\n' +
                '                <div class="form-group">\n' +
                '                    <div class="col-sm-10">\n' +
                '                        <div class="input-group">\n' +
                '                            <input type="hidden" id="contractorId' + contractorIndex + '" name="contractorId">\n' +
                '                            <input type="hidden" id="contractorCode' + contractorIndex + '" name="contractorCode">\n' +
                '                            <input type="hidden" id="contractorNameEn' + contractorIndex + '" name="contractorNameEn">\n' +
                '                            <input type="hidden" id="isUpdateContractor' + contractorIndex + '" name="isUpdateContractor" value="false">\n' +
                '                            <input type="text" class="form-control" id="contractorNameCn' +contractorIndex + '" placeholder="可通过承包商名称搜索" name="contractorNameCn">\n' +
                '                            <div class="input-group-btn">\n' +
                '                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">\n' +
                '                                    <span class="caret"></span>\n' +
                '                                </button>\n' +
                '                                <ul class="dropdown-menu dropdown-menu-right" role="menu">\n' +
                '                                </ul>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                    <div class="col-sm-1">\n' +
                '                        <a class="btn btn-white btn-bitbucket" onclick="removeContractorLabel(\'' + contractorIndex + '\')">\n' +
                '                            <i class="fa fa-remove"></i>\n' +
                '                        </a>\n' +
                '                    </div>\n' +
                '                </div>\n' +
                '            </div>';
            $("#contractorDiv").append(inventor)
            initContractorBsSuggest('contractorNameCn' + contractorIndex)
        }

        function removeContractorLabel(index) {
            //存储删除的ID
            let deleteContractorId = $('#deleteContractorId').val();
            let contractorId = $('#contractorId' + index).val();
            if (contractorId != null && contractorId != '' && contractorId != undefined){
                if (deleteContractorId != null && deleteContractorId != '' && deleteContractorId != undefined){
                    $('#deleteContractorId').val(deleteContractorId + ',' + contractorId)
                }else{
                    $('#deleteContractorId').val(contractorId)
                }
            }
            $('#contractorTemplate' + index).remove()
        }

        function initContractorBsSuggest(id) {
            let index = id.substring(id.length - 1);
            $("#" + id).bsSuggest({
                url: ctx + "organization/org/selectByKeywordAndIncludeOrgTypes?keyword=",
                //ignorecase : true,//搜索忽略大小写
                getDataMethod: 'url',//获取数据的方式，url：一直从url请求；data：从 options.data 获取；firstByUrl：第一次从Url获取全部数据，之后从options.data获取
                autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
                autoMinWidth: false,//是否自动最小宽度，设为 false 则最小宽度不小于输入框宽度
                listStyle: {
                    'padding-top': 0,
                    'max-height': '250px',
                    'max-width': '600px',
                    'overflow': 'auto',
                    'width': 'auto',
                    'transition': '0.3s',
                    '-webkit-transition': '0.3s',
                    '-moz-transition': '0.3s',
                    '-o-transition': '0.3s'
                },//列表的样式控制
                idField: "orgNameCn",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
                keyField: "orgNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
                effectiveFields: ['orgNameCn', 'orgNameEn', 'uniqueCode'],//设置展示字段
                effectiveFieldsAlias: {
                    orgNameCn: '中文名称',
                    orgNameEn: '英文名称',
                    uniqueCode: '单位编码'
                }//设置字段别名
            }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
                $("#" + id).bsSuggest("hide");
                //选择后隐藏下拉框
                $("#contractorCode" + index).val(result.orgCode);
                $("#contractorNameEn" + index).val(result.orgNameEn);
                //判断是否为数据库中的数据
                let contractorId = $('#contractorId' + index).val();
                if (contractorId != null && contractorId != '' && contractorId != undefined){
                    $('#isUpdateContractor' + index).val('true');
                }
            }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
                let background = $('#' + id).css("background-color");
                if (background == 'rgba(255, 0, 0, 0.1)'){
                    //判断是否为数据库中的数据
                    let contractorId = $('#contractorId' + index).val();
                    if (contractorId != null && contractorId != '' && contractorId != undefined){
                        $('#isUpdateContractor' + index).val('true');
                    }
                    $("#contractorCode" + index).val('');
                    $("#contractorNameEn" + index).val('');
                    $('#' + id).css("background-color", 'rgb(255, 255, 255)')
                }
            });
        }

        //图片上传
        function picture() {
            let peopleCode = $("#peopleCode").val();
            if (peopleCode == '' || peopleCode == null){
                $('#pictureInput').trigger('click');
            }
        }

        $("#pictureInput").change(function () {
            var data = new FormData();
            data.append("file", $("#pictureInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#pictureUrl").attr("src", result.url)
                        $("#picture").val(result.url)
                    }
                },
                error: function (error) {
                    alert("图片上传失败。");
                }
            });
        });
    </script>
