<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增卫星')" />
    <th:block th:include="include :: select2-css" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-satellite-add">
            <div class="row">
                <div class="col-sm-9">
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">装备分类：</label>
                                <div class="col-sm-8">
                                    <input id="treeClassificationCode" name="classificationCode" type="hidden" th:value="${classificationCode}"/>
                                    <input name="classificationName" readonly="true" th:value="${classificationName}" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">国家/地区：</label>
                                <div class="col-sm-8">
                                    <select name="homeCountry" id="homeCountry" class="form-control" th:with="type=${@dict.getType('sys_country')}" required>
                                        <option value="" style="color: #b6b6b6" disabled selected>选择国家/地区</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">部署位置：</label>
                                <div class="col-sm-8">
                                    <input name="deploymentLocation" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">卫星中文名称：</label>
                                <div class="col-sm-8">
                                    <input name="satelliteNameCn" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">卫星英文名称：</label>
                                <div class="col-sm-8">
                                    <input name="satelliteNameEn" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">在役数量：</label>
                                <div class="col-sm-8">
                                    <input name="serviceQuantity" class="form-control" type="text">
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">原产国/地区：</label>
                                <div class="col-sm-8">
                                    <select name="nationalOrigin" id="nationalOrigin" class="form-control" th:with="type=${@dict.getType('sys_country')}">
                                        <option value="" style="color: #b6b6b6" disabled selected>选择原产国/地区</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">部署年份：</label>
                                <div class="col-sm-8">
                                    <select  name="deploymentYeat" id="deploymentYeat" class="form-control">
                                        <option value="">年份</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">服役状态：</label>
                                <div class="col-sm-8">
                                    <select name="serviceStatus" id="serviceStatus" class="form-control" th:with="type=${@dict.getType('service_status')}" required>
                                        <option value="" style="color: #b6b6b6" disabled selected>选择服役状态</option>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">所属军兵种：</label>
                                <div class="col-sm-8">
                                    <select name="troopsCategory" id="troopsCategory" class="form-control" th:with="type=${@dict.getType('sys_troops_categories')}" multiple>
                                        <option th:each="dict : ${type}" th:text="${dict.dictLabel}" th:value="${dict.dictValue}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">是否显示前台：</label>
                                <div class="col-sm-8">
                                    <div class="radio check-box">
                                        <label><input type="radio" value="1" name="showHome"> <i></i> 是</label>
                                    </div>
                                    <div class="radio check-box">
                                        <label><input type="radio" checked="" value="0" name="showHome"> <i></i>否</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <label class="col-sm-4 control-label is-required">排序：</label>
                                <div class="col-sm-8">
                                    <input name="orderNum" th:value="${orderNum}" class="form-control" type="text" digits="true" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-sm-3">
                    <div class="form-group text-center">
                        <input type="hidden" name="picture" id="picture">
                        <p class="user-info-head" onclick="picture()">
                            <img class="img-lg" id="pictureUrl" th:src="@{'/img/default_satellite.png'}" th:onerror="'this.src=\'' + @{'/img/default_satellite.png'} + '\''">
                        </p>
                        <p><input type="file" id="pictureInput" style="display: none;"></p>
                    </div>
                </div>
            </div>

            <div class="row" id="contractorDiv">
                <div class="col-sm-2">
                    <div class="form-group">
                        <label class="col-sm-6 control-label">承包商：</label>
                        <div class="col-sm-1">
                            <a class="btn btn-white btn-bitbucket" onclick="addContractor()">
                                <i class="fa fa-plus"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <div class="form-group">    
                <label class="col-sm-1 control-label">研发情况：</label>
                <div class="col-sm-11">
                    <textarea name="researchSituation" class="form-control" rows="4"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-1 control-label">部署情况：</label>
                <div class="col-sm-11">
                    <textarea name="deploymentSituation" class="form-control" rows="4"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-1 control-label">主要性能：</label>
                <div class="col-sm-11">
                    <textarea name="mainPerformance" class="form-control" rows="4"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-1 control-label">运用特点：</label>
                <div class="col-sm-11">
                    <textarea name="featuredApps" class="form-control" rows="4"></textarea>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-1 control-label is-required">数据来源：</label>
                <div class="col-sm-11">
                    <input name="source" class="form-control" required>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
        <div class="col-sm-offset-5 col-sm-10">
            <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
            <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
        </div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: bootstrap-suggest-js" />
    <script th:inline="javascript">
        let prefix = ctx + "weaponry/satellite"
        $("#form-satellite-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-satellite-add').serialize());
            }
        }

        let contractorIndex = 0;
        function addContractor() {
            contractorIndex++;
            let inventor = '<div class="col-sm-3">\n' +
                '                <div class="form-group">\n' +
                '                    <div class="col-sm-10">\n' +
                '                        <div class="input-group">\n' +
                '                            <input type="hidden" id="contractorCode' + contractorIndex + '" name="contractorCode">\n' +
                '                            <input type="hidden" id="contractorNameEn' + contractorIndex + '" name="contractorNameEn">\n' +
                '                            <input type="text" class="form-control" id="contractorNameCn' +contractorIndex + '" placeholder="可通过承包商名称搜索" name="contractorNameCn">\n' +
                '                            <div class="input-group-btn">\n' +
                '                                <button type="button" class="btn btn-white dropdown-toggle" data-toggle="dropdown">\n' +
                '                                    <span class="caret"></span>\n' +
                '                                </button>\n' +
                '                                <ul class="dropdown-menu dropdown-menu-right" role="menu">\n' +
                '                                </ul>\n' +
                '                            </div>\n' +
                '                        </div>\n' +
                '                    </div>\n' +
                '                    <div class="col-sm-1">\n' +
                '                        <a class="btn btn-white btn-bitbucket" onclick="removeContractorLabel(this)">\n' +
                '                            <i class="fa fa-remove"></i>\n' +
                '                        </a>\n' +
                '                    </div>\n' +
                '                </div>\n' +
                '            </div>';
            $("#contractorDiv").append(inventor)
            initContractorBsSuggest(contractorIndex)
        }

        function removeContractorLabel(removeBtn) {
            $(removeBtn).parent().parent().remove()
        }

        function initContractorBsSuggest(index) {
            $("#contractorNameCn" + index).bsSuggest({
                url: ctx + "organization/org/selectByKeywordAndIncludeOrgTypes?keyword=",
                //ignorecase : true,//搜索忽略大小写
                getDataMethod: 'url',//获取数据的方式，url：一直从url请求；data：从 options.data 获取；firstByUrl：第一次从Url获取全部数据，之后从options.data获取
                autoSelect: false,// 键盘向上/下方向键时，是否自动选择值
                autoMinWidth: false,//是否自动最小宽度，设为 false 则最小宽度不小于输入框宽度
                listStyle: {
                    'padding-top': 0,
                    'max-height': '250px',
                    'max-width': '600px',
                    'overflow': 'auto',
                    'width': 'auto',
                    'transition': '0.3s',
                    '-webkit-transition': '0.3s',
                    '-moz-transition': '0.3s',
                    '-o-transition': '0.3s'
                },//列表的样式控制
                idField: "orgNameCn",//每组数据的哪个字段作为 data-id，优先级高于 indexId 设置
                keyField: "orgNameCn",//每组数据的哪个字段作为输入框内容，优先级高于 indexKey 设置
                effectiveFields: ['uniqueCode', 'orgNameCn', 'orgNameEn'],//设置展示字段
                effectiveFieldsAlias: {
                    uniqueCode: '单位编码',
                    orgNameCn: '中文名称',
                    orgNameEn: '英文名称'
                }//设置字段别名
            }).on('onSetSelectValue', function (e, keyword, result) {// 当从下拉菜单选取值时触发，并传回设置的数据到第二个参数
                $("#contractorNameCn" + index).bsSuggest("hide");
                //选择后隐藏下拉框
                $("#contractorCode" + index).val(result.orgCode);
                $("#contractorNameEn" + index).val(result.orgNameEn);
            }).on("blur", function (e) {//当无匹配项且失去焦点时清除编码
                let background =   $("#contractorNameCn" + index).css("background-color");
                if (background == 'rgba(255, 0, 0, 0.1)'){
                    $("#contractorCode" + index).val('');
                    $("#contractorNameEn" + index).val('');
                    $("#contractorNameCn" + index).css("background-color", 'rgb(255, 255, 255)')
                }
            });
        }

        window.onload = function() {
            let deploymentYeat = document.getElementById("deploymentYeat");
            let date = new Date();
            let selectYear = date.getFullYear();
            //组建年份选择器
            for (let i = selectYear; i >= selectYear - 150; i--) {
                deploymentYeat.options.add(new Option(i, i));
            }
        }

        //图片上传
        function picture() {
            let peopleCode = $("#peopleCode").val();
            if (peopleCode == '' || peopleCode == null){
                $('#pictureInput').trigger('click');
            }
        }

        $("#pictureInput").change(function () {
            var data = new FormData();
            data.append("file", $("#pictureInput")[0].files[0]);
            $.ajax({
                type: "POST",
                url: ctx + "common/upload/img",
                data: data,
                cache: false,
                contentType: false,
                processData: false,
                dataType: 'json',
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $("#pictureUrl").attr("src", result.url)
                        $("#picture").val(result.url)
                    }
                },
                error: function (error) {
                    alert("图片上传失败。");
                }
            });
        });
    </script>
</body>
</html>