package com.lirong.weaponry.model.service;

import java.util.List;
import java.util.Map;

import com.lirong.weaponry.model.domain.SatelliteModel;
import com.lirong.weaponry.satellite.domain.Satellite;

/**
 * 卫星型号Service接口
 *
 * <AUTHOR>
 * @date 2023-09-18
 */
public interface SatelliteModelService {
    /**
     * 查询卫星型号
     *
     * @param modelId 卫星型号ID
     * @return 卫星型号
     */
    public SatelliteModel selectSatelliteModelById(Long modelId);

    /**
     * 查询卫星型号列表
     *
     * @param satelliteModel 卫星型号
     * @return 卫星型号集合
     */
    public List<SatelliteModel> selectSatelliteModelList(SatelliteModel satelliteModel);

    /**
     * 新增卫星型号
     *
     * @param satelliteModel 卫星型号
     * @return 结果
     */
    public int insertSatelliteModel(SatelliteModel satelliteModel);

    /**
     * 修改卫星型号
     *
     * @param satelliteModel 卫星型号
     * @return 结果
     */
    public int updateSatelliteModel(SatelliteModel satelliteModel);

    /**
     * 查询卫星型号当前最大的排序号
     *
     * @return 当前卫星型号最大的排序号
     */
    public Integer selectSatelliteModelMaxOrderNum();

    /**
     * 根据卫星型号装备编码查询
     *
     * @param weaponryCode 卫星型号装备编码
     * @return 结果
     */
    public SatelliteModel selectSatelliteModelByWeaponryCode(String weaponryCode);

    /**
     * 导入校验数据格式
     *
     * @param satelliteModelList 卫星型号数据列表
     * @param filePathIndexMap   key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList       上传后的文件路径
     * @param baseDir            临时文件夹目录
     * @param updateSupport      是否更新
     */
    public List<String> verifyImportSatelliteModel(List<SatelliteModel> satelliteModelList, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport);

    /**
     * 导入卫星型号
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    public String importSatelliteModel(boolean updateSupport, String operName);

    /**
     * 根据卫星型号武器装备编码查询
     *
     * @param weaponryCodes 卫星型号武器装备编码
     * @return 结果
     */
    public List<SatelliteModel> selectSatelliteModelByWeaponryCodes(String[] weaponryCodes);
}
