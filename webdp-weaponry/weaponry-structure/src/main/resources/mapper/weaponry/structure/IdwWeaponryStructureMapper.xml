<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.weaponry.structure.mapper.IdwWeaponryStructureMapper">

    <resultMap type="com.lirong.weaponry.structure.domain.IdwWeaponryStructure" id="IdwWeaponryStructureResult">
        <result property="structureId"    column="structure_id"    />
        <result property="classificationCode"    column="classification_code"    />
        <result property="classificationStructureCode"    column="classification_structure_code"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="structureCode"    column="structure_code"    />
        <result property="parentCode"    column="parent_code"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="introduction"    column="introduction"    />
        <result property="isRelated"    column="is_related"    />
        <result property="isWeaponry"    column="is_weaponry"    />
        <result property="relatedType"    column="related_type"    />
        <result property="relatedCode"    column="related_code"    />
        <result property="variant"    column="variant"    />
        <result property="participateKeyNode"    column="participate_key_node"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <resultMap type="com.lirong.weaponry.structure.vo.SimpleStructure" id="SimpleStructure">
        <result property="code"    column="code"    />
        <result property="type"    column="type"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="descriptionCn"    column="description_cn"    />
        <result property="descriptionEn"    column="description_en"    />
    </resultMap>

    <sql id="selectIdwWeaponryStructureVo">
        SELECT
            structure_id, classification_code, classification_structure_code, weaponry_code,
            structure_code, parent_code, name_cn, name_en,
            introduction, is_related, is_weaponry, related_type,
            related_code, variant, participate_key_node, source
        FROM
            idw_weaponry_structure
    </sql>

    <!--查询武器装备结构列表-->
    <select id="selectIdwWeaponryStructureList" parameterType="com.lirong.weaponry.structure.domain.IdwWeaponryStructure" resultMap="IdwWeaponryStructureResult">
        <include refid="selectIdwWeaponryStructureVo"/>
        <where>
            is_delete = 0
            <if test="classificationCode != null  and classificationCode != ''"> and classification_code = #{classificationCode}</if>
            <if test="weaponryCode != null and weaponryCode != ''"> and weaponry_code = #{weaponryCode}</if>
            <if test="structureCode != null  and structureCode != ''"> and structure_code = #{structureCode}</if>
            <if test="parentCode != null  and parentCode != ''"> and ( parent_code = #{parentCode} OR structure_code = #{parentCode} ) </if>
            <if test="nameCn != null  and nameCn != ''">
                and (name_cn like concat('%', #{nameCn}, '%') or name_en like concat('%', #{nameCn}, '%'))
            </if>
        </where>
        ORDER BY
            parent_code
    </select>

    <!--根据武器装备结构编码查询子结构条目数-->
    <select id="selectEntriesNumberByPrentCode" resultType="java.lang.Integer">
        SELECT
        COUNT( * )
        FROM
        idw_weaponry_structure
        WHERE
        is_delete = 0
        AND parent_code = #{structureCode}
    </select>

    <select id="selectIdwWeaponryStructureById" parameterType="Long" resultMap="IdwWeaponryStructureResult">
        <include refid="selectIdwWeaponryStructureVo"/>
        where structure_id = #{structureId} and is_delete = 0
    </select>

    <!--根据关键字查询武器装备/产品信息 like查询武器装备/产品名称(中英文名称&武器装备编码)-->
    <select id="selectByKeyword" resultMap="SimpleStructure">
        SELECT
            w.weaponry_code AS code,
            '装备' AS type,
            w.name_cn AS name_cn,
            w.name_en AS name_en,
            w.introduction_cn AS description_cn,
            w.introduction_en AS description_en
        FROM
            idw_weaponry w
        WHERE
            w.is_delete = 0
            AND ( w.weaponry_code LIKE CONCAT('%',#{keyword},'%') OR w.name_cn LIKE CONCAT('%',#{keyword},'%') OR w.name_en LIKE CONCAT('%',#{keyword},'%') ) UNION ALL
        SELECT
            CONCAT( '', p.product_id ) AS code,
            '产品' AS type,
            p.product_name_cn AS name_cn,
            p.product_name_en AS name_en,
            p.introduction_cn AS description_cn,
            p.introduction_en AS description_en
        FROM
            idw_org_product p
        WHERE
            p.is_delete = 0
            AND ( p.product_name_cn LIKE CONCAT('%',#{keyword},'%') OR p.product_name_en LIKE CONCAT('%',#{keyword},'%') )
        LIMIT 10
    </select>

    <!--根据武器装备编码与父结构编码构建武器装备结构树-->
    <select id="selectStructureTreeByWeaponryCodeAndParentCode" resultMap="IdwWeaponryStructureResult">
        SELECT
            *
        FROM
            idw_weaponry_structure
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
            AND parent_code = #{parentCode}
    </select>

    <!--根据装备结构ID查询结构编码-->
    <select id="selectStructureCodesByStructureIds" resultType="java.lang.String">
        SELECT
            structure_code
        FROM
            idw_weaponry
        WHERE
            is_delete = 0
            AND structure_id IN
            <foreach item="structureId" collection="structureIds" open="(" separator="," close=")">
                #{structureId}
            </foreach>
    </select>

    <!--根据武器装备编码查询-->
    <select id="selectByWeaponryCode" resultMap="IdwWeaponryStructureResult">
         SELECT
            *
        FROM
            idw_weaponry_structure
        WHERE
            is_delete = 0
        <if test="weaponryCode != null and weaponryCode != ''">
            AND weaponry_code = #{weaponryCode}
        </if>
        ORDER BY
            ancestors
    </select>

    <!--根据武器装备结构编码查询-->
    <select id="selectAncestorsByStructureCode" resultType="java.lang.String">
        SELECT
            ancestors
        FROM
            idw_weaponry_structure
        WHERE
            is_delete = 0
        <if test="structureCode != null and structureCode != ''">
            AND structure_code = #{structureCode}
        </if>
        LIMIT 1
    </select>

    <!--根据父结构编码和结构名称查询-->
    <select id="selectByParentCodeAndName" resultMap="IdwWeaponryStructureResult">
        SELECT
            *
        FROM
            idw_weaponry_structure
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
            AND parent_code = #{parentCode}
            AND (
                name_cn = #{nameCn}
                OR name_en = #{nameCn}
            <if test="nameEn != null and nameEn != ''">
                OR name_cn =#{nameEn}
                OR name_en =#{nameEn}
            </if>
             )
             <if test="structureId != null">
                 AND structure_id != #{structureId}
             </if>
    </select>

    <!--根据结构编码查询-->
    <select id="selectByStructureCode" resultMap="IdwWeaponryStructureResult">
        SELECT
            *
        FROM
            idw_weaponry_structure
        WHERE
            is_delete = 0
            AND structure_code = #{structureCode}
    </select>

    <!--根据装备编码与结构编码查询-->
    <select id="selectByWeaponryCodeAndStructureCode" resultMap="IdwWeaponryStructureResult">
        SELECT
            *
        FROM
            idw_weaponry_structure
        WHERE
            is_delete = 0
            AND weaponry_code = #{weaponryCode}
            AND structure_code = #{structureCode}
    </select>

    <insert id="insertIdwWeaponryStructure" parameterType="com.lirong.weaponry.structure.domain.IdwWeaponryStructure" useGeneratedKeys="true" keyProperty="structureId">
        insert into idw_weaponry_structure
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="classificationCode != null and classificationCode != ''">classification_code,</if>
            <if test="weaponryCode != null and weaponryCode != ''">weaponry_code,</if>
            <if test="structureCode != null and structureCode != ''">structure_code,</if>
            <if test="parentCode != null">parent_code,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
            <if test="nameCn != null and nameCn != ''">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="introduction != null">introduction,</if>
            <if test="isRelated != null">is_related,</if>
            <if test="isWeaponry != null">is_weaponry,</if>
            <if test="relatedType != null">related_type,</if>
            <if test="relatedCode != null">related_code,</if>
            <if test="variant != null and variant != ''">variant,</if>
            <if test="participateKeyNode != null and participateKeyNode != ''">participate_key_node,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="classificationCode != null and classificationCode != ''">#{classificationCode},</if>
            <if test="weaponryCode != null and weaponryCode != ''">#{weaponryCode},</if>
            <if test="structureCode != null and structureCode != ''">#{structureCode},</if>
            <if test="parentCode != null">#{parentCode},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
            <if test="nameCn != null and nameCn != ''">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="introduction != null">#{introduction},</if>
            <if test="isRelated != null">#{isRelated},</if>
            <if test="isWeaponry != null">#{isWeaponry},</if>
            <if test="relatedType != null">#{relatedType},</if>
            <if test="relatedCode != null">#{relatedCode},</if>
            <if test="variant != null and variant != ''">#{variant},</if>
            <if test="participateKeyNode != null and participateKeyNode != ''">#{participateKeyNode},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
        </trim>
    </insert>

    <!--批量新增-->
    <insert id="insertList">
        insert into idw_weaponry_structure (
            is_delete, classification_code, weaponry_code, structure_code,
            parent_code, ancestors, name_cn, name_en,
            introduction,is_related,is_weaponry, related_type,
            related_code,variant, source, create_by,
             create_time,update_by, update_time, classification_structure_code, participate_key_node
        ) VALUES
        <foreach item="weaponryStructure" collection="list" separator=",">
            (
                0,
                #{weaponryStructure.classificationCode},
                #{weaponryStructure.weaponryCode},
                #{weaponryStructure.structureCode},
                #{weaponryStructure.parentCode},
                #{weaponryStructure.ancestors},
                #{weaponryStructure.nameCn},
                #{weaponryStructure.nameEn},
                #{weaponryStructure.introduction},
                #{weaponryStructure.isRelated},
                #{weaponryStructure.isWeaponry},
                #{weaponryStructure.relatedType},
                #{weaponryStructure.relatedCode},
                #{weaponryStructure.variant},
                #{weaponryStructure.source},
                #{weaponryStructure.createBy},
                #{weaponryStructure.createTime},
                #{weaponryStructure.updateBy},
                #{weaponryStructure.updateTime},
                #{weaponryStructure.classificationStructureCode},
                #{weaponryStructure.participateKeyNode}
            )
        </foreach>
    </insert>

    <update id="updateIdwWeaponryStructure" parameterType="com.lirong.weaponry.structure.domain.IdwWeaponryStructure">
        update idw_weaponry_structure
        <trim prefix="SET" suffixOverrides=",">
            <if test = "classificationCode != null "> classification_code = #{classificationCode }, </if>
            <if test = "weaponryCode != null "> weaponry_code = #{weaponryCode }, </if>
            <if test = "structureCode != null"> structure_code = #{structureCode }, </if>
            <if test = "parentCode != null"> parent_code = #{parentCode }, </if>
            <if test = "ancestors != null"> ancestors = #{ancestors }, </if>
            <if test = "nameCn != null"> name_cn = #{nameCn }, </if>
            <if test = "nameEn != null"> name_en = #{nameEn }, </if>
            <if test = "introduction != null"> introduction = #{introduction }, </if>
            <if test = "isRelated != null"> is_related = #{isRelated }, </if>
            <if test = "isWeaponry != null"> is_weaponry = #{isWeaponry }, </if>
            <if test = "relatedType != null"> related_type = #{relatedType }, </if>
            <if test = "relatedCode != null"> related_code = #{relatedCode }, </if>
            <if test = "variant != null"> variant = #{variant }, </if>
            <if test = "participateKeyNode != null"> participate_key_node = #{participateKeyNode }, </if>
            <if test = "source != null"> source = #{source }, </if>
            <if test = "updateBy != null"> update_by = #{updateBy }, </if>
            <if test = "updateTime != null "> update_time = #{updateTime }, </if>
        </trim>
        where structure_id = #{structureId}
    </update>

    <update id="deleteIdwWeaponryStructureById">
        update idw_weaponry_structure
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE structure_id = #{structureId}
    </update>

    <!--根据武器装备编码删除-->
    <update id="deleteByWeaponryCode">
        update idw_weaponry_structure
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE weaponry_code = #{weaponryCode}
    </update>

    <!--根据武器装备编码删除-->
    <update id="deleteByWeaponryCodes">
        update idw_weaponry_structure
        SET update_by = #{loginName},
        update_time = sysdate(),
        weaponry_code = CONCAT( #{deleteTime} , '-' , #{loginName} , '-' , weaponry_code ),
        IS_DELETE = 1
        WHERE weaponry_code in
        <foreach item="weaponryCode" collection="weaponryCodes" open="(" separator="," close=")">
            #{weaponryCode}
        </foreach>
    </update>

    <!--根据ID删除武器装备结构-->
    <update id="deleteIdwWeaponryStructureByIds">
        update idw_weaponry_structure
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE structure_id in
        <foreach item="structureId" collection="structureIds" open="(" separator="," close=")">
            #{structureId}
        </foreach>
    </update>

</mapper>