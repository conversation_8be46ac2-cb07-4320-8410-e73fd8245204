package com.lirong.weaponry.structure.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 武器装备结构对象 idw_weaponry_structure
 *
 * <AUTHOR>
 * @date 2021-04-09
 */
public class IdwWeaponryStructure extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 结构ID
     */
    private Long structureId;

    /**
     * 装备分类编码
     */
    @Excel(name = "装备分类编码")
    private String classificationCode;

    /**
     * 装备分类结构编码
     */
    private String classificationStructureCode;

    /**
     * 装备编码
     */
    @Excel(name = "装备编码")
    private String weaponryCode;

    /**
     * 结构编码
     */
    @Excel(name = "结构编码")
    private String structureCode;

    /**
     * 上级结构编码
     */
    @Excel(name = "上级结构编码")
    private String parentCode;

    /**
     * 祖籍列表
     */
    private String ancestors;

    /**
     * 上级结构名称
     */
    private String parentName;

    /**
     * 结构中文名称
     */
    @Excel(name = "结构中文名称")
    private String nameCn;

    /**
     * 结构英文名称
     */
    @Excel(name = "结构英文名称")
    private String nameEn;

    /**
     * 变体名称
     */
    private String variant;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String introduction;

    /**
     * 是否为关联装备/产品，1-是，0-否
     */
    @Excel(name = "关联装备/产品", dictType = "sys_true_or_false", combo = {"是", "否"})
    private Integer isRelated;

    /**
     * 关联类型：装备/产品
     */
    @Excel(name = "关联类型：装备/产品")
    private String relatedType;

    /**
     * 关联编码（装备编码/产品编码）
     */
    @Excel(name = "关联编码")
    private String relatedCode;

    /**
     * 关联名称
     */
    @Excel(name = "关联名称")
    private String relatedName;

    /**
     * 是否为武器装备 1 是 0 否
     */
    @Excel(name = "是否为武器装备", dictType = "sys_true_or_false", combo = {"是", "否"})
    private Integer isWeaponry;

    /**
     * 是否为重要节点 1 是 0 否
     */
    @Excel(name = "是否为重要节点", dictType = "sys_true_or_false", combo = {"是", "否"})
    private Integer participateKeyNode;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    public void setStructureId(Long structureId) {
        this.structureId = structureId;
    }

    public Long getStructureId() {
        return structureId;
    }

    public void setClassificationCode(String classificationCode) {
        this.classificationCode = classificationCode;
    }

    public String getClassificationCode() {
        return classificationCode;
    }

    public String getClassificationStructureCode() {
        return classificationStructureCode;
    }

    public void setClassificationStructureCode(String classificationStructureCode) {
        this.classificationStructureCode = classificationStructureCode;
    }

    public void setWeaponryCode(String weaponryCode) {
        this.weaponryCode = weaponryCode;
    }

    public String getWeaponryCode() {
        return weaponryCode;
    }

    public void setStructureCode(String structureCode) {
        this.structureCode = structureCode;
    }

    public String getStructureCode() {
        return structureCode;
    }

    public void setParentCode(String parentCode) {
        this.parentCode = parentCode;
    }

    public String getParentCode() {
        return parentCode;
    }

    public String getAncestors() {
        return ancestors;
    }

    public void setAncestors(String ancestors) {
        this.ancestors = ancestors;
    }

    public String getParentName() {
        return parentName;
    }

    public void setParentName(String parentName) {
        this.parentName = parentName;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public String getVariant() {
        return variant;
    }

    public void setVariant(String variant) {
        this.variant = variant;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }

    public String getIntroduction() {
        return introduction;
    }

    public void setIsRelated(Integer isRelated) {
        this.isRelated = isRelated;
    }

    public Integer getIsRelated() {
        return isRelated;
    }

    public void setRelatedType(String relatedType) {
        this.relatedType = relatedType;
    }

    public String getRelatedType() {
        return relatedType;
    }

    public void setRelatedCode(String relatedCode) {
        this.relatedCode = relatedCode;
    }

    public String getRelatedCode() {
        return relatedCode;
    }

    public String getRelatedName() {
        return relatedName;
    }

    public void setRelatedName(String relatedName) {
        this.relatedName = relatedName;
    }

    public Integer getIsWeaponry() {
        return isWeaponry;
    }

    public void setIsWeaponry(Integer isWeaponry) {
        this.isWeaponry = isWeaponry;
    }

    public Integer getParticipateKeyNode() {
        return participateKeyNode;
    }

    public void setParticipateKeyNode(Integer participateKeyNode) {
        this.participateKeyNode = participateKeyNode;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("structureId", getStructureId())
                .append("classificationCode", getClassificationCode())
                .append("classificationStructureCode", getClassificationStructureCode())
                .append("weaponryCode", getWeaponryCode())
                .append("structureCode", getStructureCode())
                .append("parentCode", getParentCode())
                .append("ancestors", getAncestors())
                .append("parentName", getParentName())
                .append("nameCn", getNameCn())
                .append("nameEn", getNameEn())
                .append("variant", getVariant())
                .append("introduction", getIntroduction())
                .append("isRelated", getIsRelated())
                .append("relatedType", getRelatedType())
                .append("relatedCode", getRelatedCode())
                .append("relatedName", getRelatedName())
                .append("isWeaponry", getIsWeaponry())
                .append("participateKeyNode", getParticipateKeyNode())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
