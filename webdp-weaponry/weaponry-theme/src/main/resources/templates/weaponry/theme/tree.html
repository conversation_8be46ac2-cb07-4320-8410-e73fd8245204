<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('武器装备主题库关联装备')" />
    <th:block th:include="include :: ztree-css" />
</head>
<style type="text/css">
    body{height:auto;font-family: "Microsoft YaHei";}
    button{font-family: "SimSun","Helvetica Neue",Helvetica,Arial;}
    .tooltips{position: fixed; z-index: 9; top: 0px; width: 100%; padding: 0px 10px; font-weight: 600; line-height: 30px; height: 30px; background-color: #d2e9f0;}
    .tips-num {padding: 0px 1px; color: crimson;}
</style>
<body class="hold-transition box box-main">
    <input name="weaponryCodes" id="weaponryCodes" th:value="${weaponryCode}" type="hidden">
    <input name="insertWeaponryCodes" id="insertWeaponryCodes" type="hidden">
    <input name="deleteWeaponryCodes" id="deleteWeaponryCodes" type="hidden">
    <div class="tooltips">
        <span>新增关联</span>(<span id="addCount" class="tips-num">0</span>) <span style="margin-left: 10px;">取消关联</span>(<span id="deleteCount" class="tips-num">0</span>)
    </div>
    <div style="margin-top: 20px;">
        <div class="wrapper"><div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
            <label id="btnShow" title="显示搜索" style="display:none;">︾</label>
            <label id="btnHide" title="隐藏搜索">︽</label>
        </div>
            <div class="treeSearchInput" id="search">
                <label for="keyword">关键字：</label><input type="text" class="empty" id="keyword" maxlength="50">
                <button class="btn" id="btn" onclick="$.tree.searchNode()"> 搜索 </button>
            </div>
            <div class="treeExpandCollapse">
                <a href="#" onclick="$.tree.expand()">展开</a> /
                <a href="#" onclick="$.tree.collapse()">折叠</a>
            </div>
            <div id="tree" class="ztree treeselect"></div>
        </div>
    </div>


    <th:block th:include="include :: footer" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        $(function() {
            let weaponryCodes = $('#weaponryCodes').val();
            let url = ctx + "weaponry/common/classificationWeaponrTree/" + ((weaponryCodes != null && weaponryCodes != undefined) ? weaponryCodes : null);
            let options = {
                id: "tree",
                url: url,
                expandLevel: 2,
                check: {
                    enable: true,
                    chkboxType: { "Y" : "s", "N" : "ps" }
                },
                beforeCheck: beforeCheck,
                onCheck: onCheck
            };
            $.tree.init(options);
        });

        //用于捕获 勾选 或 取消勾选 之前的事件回调函数，并且根据返回值确定是否允许 勾选 或 取消勾选
        function beforeCheck(treeId, treeNode) {
            let type = treeNode.orgType;    // 节点类型
            let childCount = 0; // 子节点数量
            // 判断
            if (treeNode.children) {
                childCount = treeNode.children.length;
            }
            // 如果为装备分类且不包含子节点，则禁止选中
            if (type == 'classification' && childCount == 0) {
                return false;
            } else {
                return true;
            }
        }

        /**
         * 节点选中事件
         */
        function onCheck(event, treeId, treeNode) {
            // 选中节点
            checkNodes(treeNode);
            // 更新提示
            updateTooltips();
        }

        /**
         * 更新提示信息
         */
        function updateTooltips() {
            let insertWeaponryCodes = $("#insertWeaponryCodes").val();
            let deleteWeaponryCodes = $("#deleteWeaponryCodes").val();
            if (insertWeaponryCodes !== '') {
                $("#addCount").html(insertWeaponryCodes.split(',').length - 1)
            } else {
                $("#addCount").html(0);
            }
            if (deleteWeaponryCodes !== '') {
                $("#deleteCount").html(deleteWeaponryCodes.split(',').length - 1)
            } else {
                $("#deleteCount").html(0);
            }
        }

        /**
         * 选中/取消选中
         * @param treeNode
         */
        function checkNodes(treeNode) {
            // 判断是否为装备节点
            if (treeNode.orgType == 'weaponry') {
                let code = treeNode.id + ',';
                let originalSelectedCodes = $("#weaponryCodes").val() + ',';  // 原有关联装备编码
                let insertWeaponryCodes = $("#insertWeaponryCodes").val();
                let deleteWeaponryCodes = $("#deleteWeaponryCodes").val();
                let regStr = '/' + code + '/g'  // 拼接替换文本的正则表达式
                // 选中
                if (treeNode.checked) {
                    // 判断是否为已有数据
                    if (originalSelectedCodes.indexOf(code) >= 0) {
                        deleteWeaponryCodes = deleteWeaponryCodes.replace(eval(regStr), '');
                    } else {
                        insertWeaponryCodes += code;
                    }
                } else { // 取消选中
                    // 判断是否为已有数据
                    if (originalSelectedCodes.indexOf(code) >= 0) {
                        deleteWeaponryCodes += code;    // 已有数据添加到删除列表
                    } else {
                        insertWeaponryCodes = insertWeaponryCodes.replace(eval(regStr), '');
                    }
                }
                $("#insertWeaponryCodes").val(insertWeaponryCodes);
                $("#deleteWeaponryCodes").val(deleteWeaponryCodes);
            } else {
                // 遍历分类节点
                for (let index in treeNode.children) {
                    checkNodes(treeNode.children[index])
                }
            }
        }
    </script>
</body>
</html>