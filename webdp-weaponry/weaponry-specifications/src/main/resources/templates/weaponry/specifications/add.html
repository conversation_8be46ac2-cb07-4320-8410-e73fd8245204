<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增装备规格')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-specifications-add">
            <input type="hidden" name="weaponryCode" id="weaponryCode" th:value="${weaponryCode}">
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">类型：</label>
                <div class="col-sm-8">
                    <select name="type" id="type" class="form-control" required>
                        <option value="分类">分类</option>
                        <option value="指标">指标</option>
                    </select>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">所属分类：</label>
                <div class="col-sm-8">
                    <div class="input-group">
                        <input id="parentId" name="parentId" type="hidden" th:value="${idwWeaponrySpecifications?.specificationsId}"/>
                        <input class="form-control" type="text" onclick="selectSpecificationsTree()" id="parentName" readonly="true" th:value="${idwWeaponrySpecifications?.nameCn}">
                        <span class="input-group-addon"><i class="fa fa-search"></i></span>
                    </div>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">指标中文名称：</label>
                <div class="col-sm-8">
                    <input name="nameCn" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">指标英文名称：</label>
                <div class="col-sm-8">
                    <input name="nameEn" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">指标值：</label>
                <div class="col-sm-8">
                    <textarea name="value" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">简介：</label>
                <div class="col-sm-8">
                    <textarea name="introduction" class="form-control" rows="6"></textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">排序号：</label>
                <div class="col-sm-8">
                    <input name="orderNum" th:value="${maxOrderNum}" id="orderNum" class="form-control" type="text" digits="true" required>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label is-required">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" class="form-control" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "weaponry/specifications"
        $("#form-specifications-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-specifications-add').serialize());
            }
        }

        let parentId = $('#parentId').val();
        if (parentId != null && parentId != '' && parentId != '0'){
            $('#type').val('指标');
        }

        /*装备规格-新增-选择父部门树*/
        function selectSpecificationsTree() {
            let options = {
                title: '装备规格选择',
                width: "380",
                url: prefix + "/selectSpecificationsTree/" + $("#weaponryCode").val(),
                callBack: doSubmit
            };
            $.modal.openOptions(options);
        }

        function doSubmit(index, layero){
            let body = layer.getChildFrame('body', index);
               $("#parentId").val(body.find('#parentId').val());
               $("#parentName").val(body.find('#parentName').val());
               layer.close(index);
            $.ajax({
                type : "GET",
                url : prefix + "/selectMaxOrderNum/"+ $('#weaponryCode').val()  + '/' + $('#parentId').val(),
                async : false,
                error : function(request) {
                    $.modal.alertError("系统错误");
                },
                success : function(maxOrderNum) {
                    $('#orderNum').val(maxOrderNum + 1)
                }
            });
        }
    </script>
</body>
</html>