<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('装备规格树选择')" />
    <th:block th:include="include :: ztree-css" />
</head>
<style>
    body{height:auto;font-family: "Microsoft YaHei";}
    button{font-family: "SimSun","Helvetica Neue",Helvetica,Arial;}
</style>
<body class="hold-transition box box-main">
    <input type="hidden" name="weaponryCode" id="weaponryCode" th:value="${weaponryCode}"/>
    <input id="parentId"   name="parentId" type="hidden" th:value="${idwWeaponrySpecifications?.specificationsId}"/>
    <input id="parentName" name="parentName" type="hidden" th:value="${idwWeaponrySpecifications?.nameCn}"/>
    <div class="wrapper"><div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
        <label id="btnShow" title="显示搜索" style="display:none;">︾</label>
        <label id="btnHide" title="隐藏搜索">︽</label>
    </div>
    <div class="treeSearchInput" id="search">
        <label for="keyword">关键字：</label><input type="text" class="empty" id="keyword" maxlength="50">
        <button class="btn" id="btn" onclick="$.tree.searchNode()"> 搜索 </button>
    </div>
    <div class="treeExpandCollapse">
        <a href="#" onclick="$.tree.expand()">展开</a> /
        <a href="#" onclick="$.tree.collapse()">折叠</a>
    </div>
    <div id="tree" class="ztree treeselect"></div>
    </div>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: ztree-js" />
    <script th:inline="javascript">
        $(function() {
            let url = ctx + "weaponry/specifications/treeData/" + $('#weaponryCode').val()  + '/' + $('#parentId').val();
            let options = {
                url: url,
                expandLevel: 2,
                onClick : zOnClick
            };
            $.tree.init(options);
        });

        function zOnClick(event, treeId, treeNode) {
            $("#parentId").val(treeNode.id);
            $("#parentName").val(treeNode.name);
        }
    </script>
</body>
</html>