package com.lirong.weaponry.ship.mapper;

import com.lirong.weaponry.ship.domain.Ship;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 舰船Mapper接口
 *
 * <AUTHOR>
 * @date 2023-02-02
 */
public interface ShipMapper {

    /**
     * 查询舰船
     *
     * @param id 舰船ID
     * @return 舰船
     */
    public Ship selectShipById(Long id);

    /**
     * 新增舰船
     *
     * @param ship 舰船
     * @return 结果
     */
    public int insertShip(Ship ship);

    /**
     * 修改舰船
     *
     * @param ship 舰船
     * @return 结果
     */
    public int updateShip(Ship ship);

    /**
     * 根据装备编码删除舰船
     *
     * @param shipCodes  舰船装备编码
     * @param loginName  当前登陆用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    public int deleteShipByWeaponryCodes(@Param("shipCodes") String[] shipCodes, @Param("loginName") String loginName, @Param("deleteTime") String deleteTime);

    /**
     * 根据武器装备编码查询舰船
     *
     * @param weaponryCode 武器装备编码
     * @return 结果
     */
    public Ship selectShipByWeaponryCode(String weaponryCode);

    /**
     * 根据关键字查询舰船 like查询舰船名称与舷号并排除自己
     *
     * @param weaponryCode    当前装备编码（需排除）
     * @param nationalOrigins 国家
     * @param keyword         关键词
     * @return 结果
     */
    public List<Ship> selectShipByKeyword(@Param("weaponryCode") String weaponryCode, @Param("nationalOrigins") String[] nationalOrigins, @Param("keyword") String keyword);

    /**
     * 根据舰船名称查询
     *
     * @param shipName 舰船名称
     * @return 结果
     */
    public Ship selectShipByShipName(String shipName);

    /**
     * 根据武器装备编码查询
     *
     * @param weaponryCodes 武器装备编码
     * @return 结果
     */
    public List<Ship> selectShipByWeaponryCodes(@Param("weaponryCodes") String[] weaponryCodes);

    /**
     * 查询舰船当前最大排序号
     *
     * @return 当前装备最大排序号
     */
    public Integer selectShipMaxOrderNum();

    /**
     * 根据舰船舷号查询
     *
     * @param hullNo 舰船舷号
     * @return 结果
     */
    public Ship selectShipByHullNo(String hullNo);
}
