<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.weaponry.formation.mapper.IdwWeaponryFormationMapper">
    
    <resultMap type="com.lirong.weaponry.formation.domain.IdwWeaponryFormation" id="IdwWeaponryFormationResult">
        <result property="formationId"    column="formation_id"    />
        <result property="weaponryCode"    column="weaponry_code"    />
        <result property="startDate"    column="start_date"    />
        <result property="endDate"    column="end_date"    />
        <result property="squadrons"    column="squadrons"    />
        <result property="battleGroup"    column="battle_group"    />
        <result property="areaOperations"    column="area_operations"    />
        <result property="operationsExercises"    column="operations_exercises"    />
        <result property="portsCall"    column="ports_call"    />
        <result property="source"    column="source"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="selectIdwWeaponryFormationList" parameterType="com.lirong.weaponry.formation.domain.IdwWeaponryFormation" resultMap="IdwWeaponryFormationResult">
        SELECT
            formation.formation_id,
            formation.weaponry_code,
            formation.start_date,
            formation.end_date,
            squadrons.name AS squadrons,
            ship.name AS battle_group,
            formation.area_operations,
            formation.operations_exercises,
            formation.ports_call,
            formation.source
        FROM
            idw_weaponry_formation formation
            LEFT JOIN ( SELECT formation_id, GROUP_CONCAT( name_cn ) AS name FROM idw_weaponry_formation_ship WHERE is_delete = 0 GROUP BY formation_id ) ship ON ship.formation_id = formation.formation_id
            LEFT JOIN ( SELECT formation_id, GROUP_CONCAT( name_cn ) AS name FROM idw_weaponry_formation_squadrons WHERE is_delete = 0 GROUP BY formation_id ) squadrons ON squadrons.formation_id = formation.formation_id
        WHERE
            formation.is_delete = 0
            <if test="weaponryCode != null  and weaponryCode != ''"> and formation.weaponry_code = #{weaponryCode}</if>
    </select>
    
    <select id="selectIdwWeaponryFormationById" parameterType="Long" resultMap="IdwWeaponryFormationResult">
        SELECT
            formation.formation_id,
            formation.weaponry_code,
            formation.start_date,
            formation.end_date,
            squadrons.name AS squadrons,
            ship.name AS battle_group,
            formation.area_operations,
            formation.operations_exercises,
            formation.ports_call,
            formation.source
        FROM
            idw_weaponry_formation formation
            LEFT JOIN ( SELECT formation_id, GROUP_CONCAT( name_cn ) AS name FROM idw_weaponry_formation_ship WHERE is_delete = 0 GROUP BY formation_id ) ship ON ship.formation_id = formation.formation_id
            LEFT JOIN ( SELECT formation_id, GROUP_CONCAT( name_cn ) AS name FROM idw_weaponry_formation_squadrons WHERE is_delete = 0 GROUP BY formation_id ) squadrons ON squadrons.formation_id = formation.formation_id
        WHERE
            formation.is_delete = 0
            AND formation.formation_id = #{formationId}
    </select>
        
    <insert id="insertIdwWeaponryFormation" parameterType="com.lirong.weaponry.formation.domain.IdwWeaponryFormation" useGeneratedKeys="true" keyProperty="formationId">
        insert into idw_weaponry_formation
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="weaponryCode != null and weaponryCode != ''">weaponry_code,</if>
            <if test="startDate != null and startDate != ''">start_date,</if>
            <if test="endDate != null">end_date,</if>
            <if test="areaOperations != null">area_operations,</if>
            <if test="operationsExercises != null">operations_exercises,</if>
            <if test="portsCall != null">ports_call,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="weaponryCode != null and weaponryCode != ''">#{weaponryCode},</if>
            <if test="startDate != null and startDate != ''">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="areaOperations != null">#{areaOperations},</if>
            <if test="operationsExercises != null">#{operationsExercises},</if>
            <if test="portsCall != null">#{portsCall},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateIdwWeaponryFormation" parameterType="com.lirong.weaponry.formation.domain.IdwWeaponryFormation">
        update idw_weaponry_formation
        <trim prefix="SET" suffixOverrides=",">
            <if test="weaponryCode != null and weaponryCode != ''">weaponry_code = #{weaponryCode},</if>
            <if test="startDate != null and startDate != ''">start_date = #{startDate},</if>
            <if test="endDate != null">end_date = #{endDate},</if>
            <if test="areaOperations != null">area_operations = #{areaOperations},</if>
            <if test="operationsExercises != null">operations_exercises = #{operationsExercises},</if>
            <if test="portsCall != null">ports_call = #{portsCall},</if>
            <if test="source != null and source != ''">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where formation_id = #{formationId}
    </update>

    <update id="deleteIdwWeaponryFormationByIds">
        UPDATE idw_weaponry_formation
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE formation_id in
        <foreach item="formationId" collection="formationIds" open="(" separator="," close=")">
            #{formationId}
        </foreach>
    </update>

</mapper>