<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增武器装备航母编队舰船')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-battleship-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">航母编队ID：</label>
                <div class="col-sm-8">
                    <input name="formationId" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">舷号：</label>
                <div class="col-sm-8">
                    <input name="hullNo" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">是否删除，0-未删除，1-已删除：</label>
                <div class="col-sm-8">
                    <input name="isDelete" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">舰船中文名称：</label>
                <div class="col-sm-8">
                    <input name="nameCn" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">舰船英文名称：</label>
                <div class="col-sm-8">
                    <input name="nameEn" class="form-control" type="text">
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "weaponry/battleship"
        $("#form-battleship-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/add", $('#form-battleship-add').serialize());
            }
        }
    </script>
</body>
</html>