package com.lirong.weaponry.formation.service;

import com.lirong.weaponry.formation.domain.IdwWeaponryFormationShip;

import java.util.List;

/**
 * 武器装备舰艇编队舰船Service接口
 *
 * <AUTHOR>
 * @date 2023-08-02
 */
public interface IdwWeaponryFormationShipService {

    /**
     * 新增武器装备舰艇编队舰船
     *
     * @param idwWeaponryFormationShip 武器装备舰艇编队舰船
     * @return 结果
     */
    public int insertIdwWeaponryFormationShip(IdwWeaponryFormationShip idwWeaponryFormationShip);

    /**
     * 修改武器装备舰艇编队舰船
     *
     * @param idwWeaponryFormationShip 武器装备舰艇编队舰船
     * @return 结果
     */
    public int updateIdwWeaponryFormationShip(IdwWeaponryFormationShip idwWeaponryFormationShip);

    /**
     * 批量删除武器装备舰艇编队舰船
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteIdwWeaponryFormationShipByIds(String ids);

    /**
     * 根据舰艇编队ID查询舰艇编队舰船
     *
     * @param formationId 舰艇编队ID
     * @return 结果
     */
    public List<IdwWeaponryFormationShip> selectWeaponryFormationShipByFormationId(Long formationId);
}
