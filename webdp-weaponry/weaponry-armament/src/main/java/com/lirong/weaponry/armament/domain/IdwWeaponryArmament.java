package com.lirong.weaponry.armament.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 装备配备的武器对象 idw_weaponry_armament
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
public class IdwWeaponryArmament extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 武器ID
     */
    private Long armamentId;

    /**
     * 装备编码
     */
    @Excel(name = "装备编码")
    private String weaponryCode;

    /**
     * 武器编码(装备编码)
     */
    @Excel(name = "武器编码")
    private String armamentCode;

    /**
     * 中文名称
     */
    @Excel(name = "中文名称")
    private String armamentNameCn;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String armamentNameEn;

    /**
     * 武器分类
     */
    @Excel(name = "武器分类")
    private String category;

    /**
     * 简介
     */
    @Excel(name = "简介")
    private String profile;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    public void setArmamentId(Long armamentId) {
        this.armamentId = armamentId;
    }

    public Long getArmamentId() {
        return armamentId;
    }

    public void setWeaponryCode(String weaponryCode) {
        this.weaponryCode = weaponryCode;
    }

    public String getWeaponryCode() {
        return weaponryCode;
    }

    public void setArmamentCode(String armamentCode) {
        this.armamentCode = armamentCode;
    }

    public String getArmamentCode() {
        return armamentCode;
    }

    public void setArmamentNameCn(String armamentNameCn) {
        this.armamentNameCn = armamentNameCn;
    }

    public String getArmamentNameCn() {
        return armamentNameCn;
    }

    public void setArmamentNameEn(String armamentNameEn) {
        this.armamentNameEn = armamentNameEn;
    }

    public String getArmamentNameEn() {
        return armamentNameEn;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getCategory() {
        return category;
    }

    public void setProfile(String profile) {
        this.profile = profile;
    }

    public String getProfile() {
        return profile;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("armamentId", getArmamentId())
                .append("weaponryCode", getWeaponryCode())
                .append("armamentCode", getArmamentCode())
                .append("armamentNameCn", getArmamentNameCn())
                .append("armamentNameEn", getArmamentNameEn())
                .append("category", getCategory())
                .append("profile", getProfile())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
