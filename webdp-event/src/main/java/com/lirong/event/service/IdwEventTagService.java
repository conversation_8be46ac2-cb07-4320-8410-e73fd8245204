package com.lirong.event.service;

import java.util.List;

import com.lirong.event.domain.IdwEventTag;

/**
 * 事件标签Service接口
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
public interface IdwEventTagService {
    /**
     * 查询事件标签
     *
     * @param tagId 事件标签ID
     * @return 事件标签
     */
    public IdwEventTag selectIdwEventTagById(Long tagId);

    /**
     * 查询事件标签列表
     *
     * @param idwEventTag 事件标签
     * @return 事件标签集合
     */
    public List<IdwEventTag> selectIdwEventTagList(IdwEventTag idwEventTag);

    /**
     * 新增事件标签
     *
     * @param idwEventTag 事件标签
     * @return 结果
     */
    public int insertIdwEventTag(IdwEventTag idwEventTag);

    /**
     * 修改事件标签
     *
     * @param idwEventTag 事件标签
     * @return 结果
     */
    public int updateIdwEventTag(IdwEventTag idwEventTag);

    /**
     * 批量删除事件标签
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteIdwEventTagByIds(String ids);
}
