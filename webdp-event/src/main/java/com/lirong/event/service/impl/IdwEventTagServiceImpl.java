package com.lirong.event.service.impl;

import java.util.List;

import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.event.mapper.IdwEventTagMapper;
import com.lirong.event.domain.IdwEventTag;
import com.lirong.event.service.IdwEventTagService;
import com.lirong.common.core.text.Convert;

/**
 * 事件标签Service业务层处理
 *
 * <AUTHOR>
 * @date 2022-11-23
 */
@Service
public class IdwEventTagServiceImpl implements IdwEventTagService {
    @Autowired
    private IdwEventTagMapper idwEventTagMapper;

    /**
     * 查询事件标签
     *
     * @param tagId 事件标签ID
     * @return 事件标签
     */
    @Override
    public IdwEventTag selectIdwEventTagById(Long tagId) {
        return idwEventTagMapper.selectIdwEventTagById(tagId);
    }

    /**
     * 查询事件标签列表
     *
     * @param idwEventTag 事件标签
     * @return 事件标签
     */
    @Override
    public List<IdwEventTag> selectIdwEventTagList(IdwEventTag idwEventTag) {
        return idwEventTagMapper.selectIdwEventTagList(idwEventTag);
    }

    /**
     * 新增事件标签
     *
     * @param idwEventTag 事件标签
     * @return 结果
     */
    @Override
    public int insertIdwEventTag(IdwEventTag idwEventTag) {
        idwEventTag.setCreateBy(ShiroUtils.getUserName());
        idwEventTag.setCreateTime(DateUtils.getNowDate());
        return idwEventTagMapper.insertIdwEventTag(idwEventTag);
    }

    /**
     * 修改事件标签
     *
     * @param idwEventTag 事件标签
     * @return 结果
     */
    @Override
    public int updateIdwEventTag(IdwEventTag idwEventTag) {
        idwEventTag.setUpdateBy(ShiroUtils.getUserName());
        idwEventTag.setUpdateTime(DateUtils.getNowDate());
        return idwEventTagMapper.updateIdwEventTag(idwEventTag);
    }

    /**
     * 删除事件标签对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwEventTagByIds(String ids) {
        return idwEventTagMapper.deleteIdwEventTagByIds(Convert.toStrArray(ids), ShiroUtils.getUserName());
    }
}
