package com.lirong.patent.mapper;

import java.util.List;

import com.lirong.patent.domain.Patent;

/**
 * 专利Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-24
 */
public interface PatentMapper {
    /**
     * 查询专利
     *
     * @param id 专利ID
     * @return 专利
     */
    public Patent selectPatentById(Long id);

    /**
     * 查询专利列表
     *
     * @param patent 专利
     * @return 专利集合
     */
    public List<Patent> selectPatentList(Patent patent);

    /**
     * 新增专利
     *
     * @param patent 专利
     * @return 结果
     */
    public int insertPatent(Patent patent);

    /**
     * 修改专利
     *
     * @param patent 专利
     * @return 结果
     */
    public int updatePatent(Patent patent);

    /**
     * 批量删除专利
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deletePatentByIds(String[] ids);

    /**
     * 批量删除专利机构
     *
     * @param ids 专利发明ID
     * @return 结果
     */
    public int deletePatentOrgByIds(String[] ids);
}
