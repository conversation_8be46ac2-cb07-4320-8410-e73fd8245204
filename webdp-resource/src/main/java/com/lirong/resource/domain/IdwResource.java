package com.lirong.resource.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;

import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 资源库对象 idw_resource
 *
 * <AUTHOR>
 * @date 2021-05-31
 */
public class IdwResource extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 资源ID
     */
    private Long resourceId;

    /**
     * 国家
     */
    @Excel(name = "国家", dictType = "sys_country")
    private String country;

    /**
     * 资源中文名称
     */
    @Excel(name = "中文名称|中文译名")
    private String nameCn;

    /**
     * 资源英文名称
     */
    @Excel(name = "英文名称|英文标题")
    private String nameEn;

    /**
     * 缩略图
     */
    @Excel(name = "缩略图")
    private String thumbnail;

    /**
     * 发布机构
     */
    @Excel(name = "发布机构")
    private String publisher;

    /**
     * 发布时间
     */
    @Excel(name = "发布日期")
    private String publishDate;

    private String year;
    private String month;
    private String day;

    /**
     * 作者
     */
    @Excel(name = "作者")
    private String author;

    /**
     * 资源库类别Id 关联资源库所属分类表
     */
    private String categoryIds;

    /**
     * 中文摘要
     */
    @Excel(name = "中文摘要")
    private String summaryCn;

    /**
     * 英文摘要
     */
    @Excel(name = "英文摘要|摘要")
    private String summaryEn;

    /**
     * 关键词
     */
    @Excel(name = "关键词")
    private String keyword;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称")
    private String fileName;

    /**
     * 存储路径
     */
    @Excel(name = "文献")
    private String storagePath;

    /**
     * 页数
     */
    @Excel(name = "页数")
    private Integer pageNum;

    /**
     * 文件类型
     */
    @Excel(name = "文件类型")
    private String fileType;

    /**
     * 是否显示前台
     */
    private Integer showHome;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 资源库类别名称
     */
    @Excel(name = "所属类别|分类")
    private String categoryName;

    /**
     * 资源库类别ID
     */
    private Long categoryId;

    public void setResourceId(Long resourceId) {
        this.resourceId = resourceId;
    }

    public Long getResourceId() {
        return resourceId;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getCountry() {
        return StringUtils.isNotBlank(country) ? country.trim() : country;
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameCn() {
        return nameCn;
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getNameEn() {
        return nameEn;
    }

    public void setThumbnail(String thumbnail) {
        this.thumbnail = thumbnail;
    }

    public String getThumbnail() {
        return thumbnail;
    }

    public void setPublisher(String publisher) {
        this.publisher = publisher;
    }

    public String getPublisher() {
        return publisher;
    }

    public void setPublishDate(String publishDate) {
        this.publishDate = publishDate;
    }

    public String getPublishDate() {
        return publishDate;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        if (StringUtils.isNotEmpty(month) && month.length() < 2) {
            return "0" + month;
        } else {
            return month;
        }
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDay() {
        if (StringUtils.isNotEmpty(day) && day.length() < 2) {
            return "0" + day;
        } else {
            return day;
        }
    }

    public void setDay(String day) {
        this.day = day;
    }

    public void setAuthor(String author) {
        this.author = author;
    }

    public String getAuthor() {
        return author;
    }

    public String getCategoryIds() {
        return categoryIds;
    }

    public void setCategoryIds(String categoryIds) {
        this.categoryIds = categoryIds;
    }

    public void setSummaryCn(String summaryCn) {
        this.summaryCn = summaryCn;
    }

    public String getSummaryCn() {
        return summaryCn;
    }

    public void setSummaryEn(String summaryEn) {
        this.summaryEn = summaryEn;
    }

    public String getSummaryEn() {
        return summaryEn;
    }

    public void setKeyword(String keyword) {
        this.keyword = keyword;
    }

    public String getKeyword() {
        return keyword;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public void setStoragePath(String storagePath) {
        this.storagePath = storagePath;
    }

    public String getStoragePath() {
        return storagePath;
    }

    public void setPageNum(Integer pageNum) {
        this.pageNum = pageNum;
    }

    public Integer getPageNum() {
        return pageNum;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    public String getFileType() {
        return fileType;
    }

    public Integer getShowHome() {
        return showHome;
    }

    public void setShowHome(Integer showHome) {
        this.showHome = showHome;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public String getCategoryName() {
        return categoryName;
    }

    public void setCategoryName(String categoryName) {
        this.categoryName = categoryName;
    }

    public Long getCategoryId() {
        return categoryId;
    }

    public void setCategoryId(Long categoryId) {
        this.categoryId = categoryId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("resourceId", getResourceId())
                .append("country", getCountry())
                .append("nameCn", getNameCn())
                .append("nameEn", getNameEn())
                .append("thumbnail", getThumbnail())
                .append("publisher", getPublisher())
                .append("publishDate", getPublishDate())
                .append("year", getYear())
                .append("month", getMonth())
                .append("day", getDay())
                .append("author", getAuthor())
                .append("categoryIds", getCategoryIds())
                .append("summaryCn", getSummaryCn())
                .append("summaryEn", getSummaryEn())
                .append("keyword", getKeyword())
                .append("fileName", getFileName())
                .append("storagePath", getStoragePath())
                .append("pageNum", getPageNum())
                .append("fileType", getFileType())
                .append("showHome", getShowHome())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("categoryName", getCategoryName())
                .append("categoryId", getCategoryId())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
