<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.resource.mapper.IdwResourceDictCategoryMapper">
    
    <resultMap type="com.lirong.resource.domain.IdwResourceDictCategory" id="IdwResourceDictCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="field"    column="field"    />
        <result property="parentId"    column="parent_id"    />
        <result property="ancestors"    column="ancestors"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="parentName" column="parent_name" />
    </resultMap>

    <resultMap  type="com.lirong.common.core.domain.Ztree" id="ztreeResult">
        <result property="id" column="id"/>
        <result property="pId" column="parent_id"/>
        <result property="name" column="name"/>
        <result property="nameEn" column="name_en"/>
        <result property="title" column="title"/>
        <result property="checked" column="checked"/>
        <result property="open" column="open"/>
        <result property="nocheck" column="nocheck"/>
    </resultMap>

    <sql id="selectIdwResourceDictCategoryVo">
        SELECT
            category_id,
            category_name,
            field,
            parent_id,
            ancestors
        FROM
            idw_resource_dict_category
    </sql>

    <select id="selectIdwResourceDictCategoryList" parameterType="com.lirong.resource.domain.IdwResourceDictCategory" resultMap="IdwResourceDictCategoryResult">
        <include refid="selectIdwResourceDictCategoryVo"/>
        <where>
            is_delete = 0
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="field != null  and field != ''"> and field like concat('%', #{field}, '%')</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
        </where>
        order by parent_id
    </select>

    <select id="selectIdwResourceDictCategoryById" parameterType="Long" resultMap="IdwResourceDictCategoryResult">
        select t.category_id, t.category_name, t.field, t.parent_id, t.ancestors, t.is_delete, t.create_by, t.create_time, t.update_by, t.update_time, p.category_name as parent_name
        from idw_resource_dict_category t
        left join idw_resource_dict_category p on p.category_id = t.parent_id and p.is_delete = 0
        where t.category_id = #{categoryId} and t.is_delete = 0
    </select>

    <!--查询资源库类别树列表-->
    <select id="buildResourceDictCategoryTree" resultMap="ztreeResult" parameterType="Long">
        SELECT
            category_id AS id,
            parent_id,
            category_name AS name,
            category_name AS title
        FROM
            idw_resource_dict_category
        WHERE
            is_delete = 0
            <if test="categoryId != null">
                AND ! FIND_IN_SET( #{categoryId}, ancestors )
                AND category_id != #{categoryId}
            </if>
    </select>

    <!--构建资源库类别树-->
    <select id="resourceCategoryTreeData" resultMap="ztreeResult">
        SELECT
            category.category_id AS id,
            category.parent_id,
            category.category_name AS name,
            category.category_name AS title,
            FALSE AS open,
        <if test="databaseType != null and databaseType == 'DM DBMS'">
            CASE WHEN ( SELECT COUNT( * ) FROM idw_resource_dict_category dict WHERE dict.is_delete = 0 AND dict.parent_id = category.category_id ) = 0 THEN FALSE ELSE FALSE END AS is_exist
        </if>
        <if test="databaseType != null and databaseType == 'MySQL'">
            IF
            (
                ( SELECT COUNT( * ) FROM idw_resource_dict_category dict WHERE dict.is_delete = 0 AND dict.parent_id = category.category_id ) = 0,
            FALSE,
            TRUE
            ) AS nocheck
        </if>
        FROM
            idw_resource_dict_category category
        WHERE
            category.is_delete = 0
    </select>

    <!--查询最大的ID-->
    <select id="selectMaxCategoryId" resultType="java.lang.Long">
        SELECT
            MAX( category_id ) AS max_category_id
        FROM
            idw_resource_dict_category
    </select>

    <!--根据资源库类别名称查询-->
    <select id="selectByCategoryName" resultMap="IdwResourceDictCategoryResult">
        SELECT
            *
        FROM
            idw_resource_dict_category
        WHERE
            is_delete = 0
            AND category_name = #{categoryName}
    </select>

    <insert id="insertIdwResourceDictCategory" parameterType="com.lirong.resource.domain.IdwResourceDictCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into idw_resource_dict_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="field != null and field != ''">field,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="ancestors != null and ancestors != ''">ancestors,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="field != null and field != ''">#{field},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="ancestors != null and ancestors != ''">#{ancestors},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateIdwResourceDictCategory" parameterType="com.lirong.resource.domain.IdwResourceDictCategory">
        update idw_resource_dict_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null">category_name = #{categoryName},</if>
            <if test="field != null and field != ''">field = #{field},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="ancestors != null">ancestors = #{ancestors},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <update id="deleteIdwResourceDictCategoryById">
        update idw_resource_dict_category
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE category_id = #{categoryId}
    </update>

</mapper>