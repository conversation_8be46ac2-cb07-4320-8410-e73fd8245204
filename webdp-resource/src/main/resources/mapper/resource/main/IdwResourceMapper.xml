<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.resource.mapper.IdwResourceMapper">

    <resultMap type="com.lirong.resource.domain.IdwResource" id="IdwResourceResult">
        <result property="resourceId"    column="resource_id"    />
        <result property="country"    column="country"    />
        <result property="nameCn"    column="name_cn"    />
        <result property="nameEn"    column="name_en"    />
        <result property="thumbnail"    column="thumbnail"    />
        <result property="publisher"    column="publisher"    />
        <result property="publishDate"    column="publish_date"    />
        <result property="author"    column="author"    />
        <result property="categoryIds"    column="category_id"    />
        <result property="summaryCn"    column="summary_cn"    />
        <result property="summaryEn"    column="summary_en"    />
        <result property="keyword"    column="keyword"    />
        <result property="fileName"    column="file_name"    />
        <result property="storagePath"    column="storage_path"    />
        <result property="pageNum"    column="page_num"    />
        <result property="fileType"    column="file_type"    />
        <result property="showHome"    column="show_home"    />
        <result property="source"    column="source"    />
        <result property="categoryName"    column="category_name"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <select id="selectIdwResourceList" parameterType="com.lirong.resource.domain.IdwResource" resultMap="IdwResourceResult">
        SELECT
            resource.resource_id, resource.country, resource.name_cn, resource.name_en,
            resource.thumbnail, resource.publisher, resource.publish_date, resource.author,
            resource.summary_cn, resource.summary_en, resource.keyword, resource.file_name,
            resource.storage_path, resource.page_num, resource.file_type, resource.source,
            dict.category_name AS category_name
        FROM
            idw_resource resource
            JOIN idw_resource_category category ON category.is_delete = 0
            AND resource.resource_id = category.resource_id
            JOIN idw_resource_dict_category dict ON dict.is_delete = 0
            AND dict.category_id = category.category_id
        WHERE
            resource.is_delete = 0
        <if test="categoryId != null">
            AND FIND_IN_SET( #{categoryId}, dict.ancestors )
        </if>
        <if test="country != null and country != ''">
            AND resource.country = #{country}
        </if>
        <if test="nameCn != null and nameCn != ''">
            AND ( resource.name_cn LIKE concat( '%', #{nameCn}, '%' ) OR resource.name_en LIKE concat( '%', #{nameCn}, '%' ) )
        </if>
        <if test="keyword != null and keyword != ''">
            AND FIND_IN_SET( #{keyword}, resource.keyword )
        </if>
    </select>

    <select id="selectIdwResourceById" parameterType="Long" resultMap="IdwResourceResult">
        SELECT
            resource.resource_id,
            resource.country,
            resource.name_cn,
            resource.name_en,
            resource.thumbnail,
            resource.publisher,
            resource.publish_date,
            resource.author,
            resource.summary_cn,
            resource.summary_en,
            resource.keyword,
            resource.file_name,
            resource.storage_path,
            resource.page_num,
            resource.file_type,
            resource.source,
            resource.show_home,
            category.category_id AS category_id
        FROM
            idw_resource resource
            JOIN (
                SELECT
                    resource_id,
                <if test="databaseType != null and databaseType == 'DM DBMS'">
                    wm_concat ( category_id ) AS category_id
                </if>
                <if test="databaseType != null and databaseType == 'MySQL'">
                    GROUP_CONCAT( category_id ) AS category_id
                </if>
                FROM
                    idw_resource_category
                WHERE
                    is_delete = 0
                GROUP BY
                    resource_id
                ) category ON category.resource_id = resource.resource_id
        WHERE
            resource.is_delete = 0
            AND resource.resource_id = #{resourceId}
    </select>

    <!--根据文件名称查询匹配条目数-->
    <select id="selectCountByFileName" resultType="java.lang.Integer">
         SELECT
            count( * )
        FROM
            idw_resource
        WHERE
            is_delete = 0
            AND file_name = #{fileName}
    </select>

    <!--根据条件查询资源库总数-->
    <select id="selectCountExcludeCreateUser" resultType="java.lang.Long">
        SELECT
            COUNT( * ) AS count
        FROM
            idw_resource
        WHERE
            is_delete = 0
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
    </select>

    <sql id="dmLoadTrendExcludeCreateUser">
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        group by
            trunc(create_time, 'iw'),
            DATE_FORMAT( create_time, '%Y-%u' )
    </sql>
    <sql id="mySqlLoadTrendExcludeCreateUser">
        AND DATE_SUB( CURDATE( ), INTERVAL 365 DAY ) &lt;= date( create_time )
        <if test="createUserNotIs != null and createUserNotIs.length > 0">
            AND create_by NOT IN
            <foreach item="createUser" collection="createUserNotIs" open="(" separator="," close=")">
                #{createUser}
            </foreach>
        </if>
        GROUP BY
            DATE_FORMAT( create_time, '%Y-%u' )
    </sql>
    <!--根据创建时间构建趋势图(按周统计)-->
    <select id="loadTrendExcludeCreateUser" resultType="com.lirong.common.vo.StatisticsVO">
        SELECT
            DATE_FORMAT( create_time, '%Y-%u' ) AS name,
            COUNT( * ) AS value
        FROM
        idw_resource
        WHERE
            is_delete = 0
        <if test="databaseType != null and databaseType == 'DM DBMS'">
            <include refid="dmLoadTrendExcludeCreateUser"/>
        </if>
        <if test="databaseType != null and databaseType == 'MySQL'">
            <include refid="mySqlLoadTrendExcludeCreateUser"/>
        </if>
    </select>

    <!--获取文件路径-->
    <select id="selectAllFilePath" resultType="java.lang.String">
        SELECT
            thumbnail
        FROM
            idw_resource
        WHERE
            is_delete = 0
            AND thumbnail IS NOT NULL
            AND thumbnail != '' UNION
        SELECT
            storage_path
        FROM
            idw_resource
        WHERE
            is_delete = 0
            AND storage_path IS NOT NULL
            AND storage_path != ''
    </select>

    <!--根据国家与名称查询-->
    <select id="selectByCountryAndName" resultMap="IdwResourceResult">
        SELECT
            *
        FROM
            idw_resource
        WHERE
            is_delete = 0
        <if test="country != null and country != ''">
            AND country = #{country}
        </if>
        <if test="nameCn != null and nameCn != ''">
            AND name_cn = #{nameCn}
        </if>
        <if test="nameEn != null and nameEn != ''">
            AND name_en = #{nameEn}
        </if>
    </select>

    <insert id="insertIdwResource" parameterType="com.lirong.resource.domain.IdwResource" useGeneratedKeys="true" keyProperty="resourceId">
        insert into idw_resource
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="country != null and country != ''">country,</if>
            <if test="nameCn != null">name_cn,</if>
            <if test="nameEn != null">name_en,</if>
            <if test="thumbnail != null">thumbnail,</if>
            <if test="publisher != null">publisher,</if>
            <if test="publishDate != null">publish_date,</if>
            <if test="author != null">author,</if>
            <if test="summaryCn != null">summary_cn,</if>
            <if test="summaryEn != null">summary_en,</if>
            <if test="keyword != null">keyword,</if>
            <if test="fileName != null">file_name,</if>
            <if test="storagePath != null">storage_path,</if>
            <if test="pageNum != null">page_num,</if>
            <if test="fileType != null">file_type,</if>
            <if test="showHome != null">show_home,</if>
            <if test="source != null and source != ''">source,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null and updateBy != ''">update_by,</if>
            <if test="updateTime != null">update_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="country != null and country != ''">#{country},</if>
            <if test="nameCn != null">#{nameCn},</if>
            <if test="nameEn != null">#{nameEn},</if>
            <if test="thumbnail != null">#{thumbnail},</if>
            <if test="publisher != null">#{publisher},</if>
            <if test="publishDate != null">#{publishDate},</if>
            <if test="author != null">#{author},</if>
            <if test="summaryCn != null">#{summaryCn},</if>
            <if test="summaryEn != null">#{summaryEn},</if>
            <if test="keyword != null">#{keyword},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="storagePath != null">#{storagePath},</if>
            <if test="pageNum != null">#{pageNum},</if>
            <if test="fileType != null">#{fileType},</if>
            <if test="showHome != null">#{showHome},</if>
            <if test="source != null and source != ''">#{source},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
         </trim>
    </insert>

    <update id="updateIdwResource" parameterType="com.lirong.resource.domain.IdwResource">
        update idw_resource
        <trim prefix="SET" suffixOverrides=",">
            <if test="country != null">country = #{country},</if>
            <if test="nameCn != null">name_cn = #{nameCn},</if>
            <if test="nameEn != null">name_en = #{nameEn},</if>
            <if test="thumbnail != null">thumbnail = #{thumbnail},</if>
            <if test="publisher != null">publisher = #{publisher},</if>
            <if test="publishDate != null">publish_date = #{publishDate},</if>
            <if test="author != null">author = #{author},</if>
            <if test="summaryCn != null">summary_cn = #{summaryCn},</if>
            <if test="summaryEn != null">summary_en = #{summaryEn},</if>
            <if test="keyword != null">keyword = #{keyword},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="storagePath != null">storage_path = #{storagePath},</if>
            <if test="pageNum != null">page_num = #{pageNum},</if>
            <if test="fileType != null">file_type = #{fileType},</if>
            <if test="showHome != null">show_home = #{showHome},</if>
            <if test="source != null">source = #{source},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where resource_id = #{resourceId}
    </update>

    <update id="deleteIdwResourceByIds">
        update idw_resource
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE resource_id in
        <foreach item="resourceId" collection="resourceIds" open="(" separator="," close=")">
            #{resourceId}
        </foreach>
    </update>

    <!--根据文件名称修改文件路径-->
    <update id="updateLinkUrlByFileName">
        update idw_resource
        SET update_by = #{loginName},
        update_time = sysdate(),
        storage_path = #{storagePath}
        WHERE
            is_delete = 0
            AND file_name = #{fileName}
    </update>

</mapper>