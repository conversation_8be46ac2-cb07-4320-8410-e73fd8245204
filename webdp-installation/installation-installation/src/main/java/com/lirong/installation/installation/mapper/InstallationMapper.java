package com.lirong.installation.installation.mapper;

import java.util.List;

import com.lirong.installation.installation.domain.Installation;
import org.apache.ibatis.annotations.Param;

/**
 * 军事设施Mapper接口
 *
 * <AUTHOR>
 * @date 2023-10-25
 */
public interface InstallationMapper {
    /**
     * 查询军事设施
     *
     * @param id 军事设施ID
     * @return 军事设施
     */
    public Installation selectInstallationById(Long id);

    /**
     * 查询军事设施列表
     *
     * @param installation 军事设施
     * @return 军事设施集合
     */
    public List<Installation> selectInstallationList(Installation installation);

    /**
     * 新增军事设施
     *
     * @param installation 军事设施
     * @return 结果
     */
    public int insertInstallation(Installation installation);

    /**
     * 修改军事设施
     *
     * @param installation 军事设施
     * @return 结果
     */
    public int updateInstallation(Installation installation);

    /**
     * 批量删除军事设施
     *
     * @param installationCodes 需要删除的数据编码
     * @param loginName         当前登录用户
     * @return 结果
     */
    public int deleteInstallationByInstallationCodes(@Param("installationCodes") String[] installationCodes, @Param("loginName") String loginName);

    /**
     * 查询最大排序号
     *
     * @return 结果
     */
    public int selectMaxOrderNum();

    /**
     * 根据军事设施编码查询
     *
     * @param installationCode 军事设施编码
     * @return 结果
     */
    public Installation selectInstallationByInstallationCode(String installationCode);
}
