<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
	<th:block th:include="include :: header('修改生成信息')" />
	<th:block th:include="include :: select2-css" />
	<style type="text/css">
	    .select-table table{table-layout:fixed;}.table>thead>tr>th{text-align:center;}.select-table .table td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}.form-control{padding:3px 6px 4px;height:30px;}.icheckbox-blue{top:0px;left:6px;}.form-control.select2-hidden-accessible{position:static!important;}.select-table table label.error{position: inherit;}select + label.error{z-index:1;right:40px;}
	</style>
</head>
<body class="gray-bg" style="font: 14px Helvetica Neue, Helvetica, PingFang SC, 微软雅黑, Tahoma, Arial, sans-serif !important;">
    <section class="section-content">
    <div class="row">
        <div class="col-xs-12">
            <div class="ibox float-e-margins">
                <div class="ibox-content" style="border-style:none;">
                    <div class="nav-tabs-custom">
                        <ul class="nav nav-tabs">
                            <li><a href="#tab-basic" data-toggle="tab" aria-expanded="false">基本信息</a></li>
                            <li class="active"><a href="#tab-field" data-toggle="tab" aria-expanded="true">字段信息</a></li>
                            <li><a href="#tab-gen" data-toggle="tab" aria-expanded="false">生成信息</a></li>
                            <li class="pull-right header">
								<i class="fa fa-code"></i> 生成配置
							</li>
                        </ul>
                        <form id="form-gen-edit" class="form-horizontal" th:object="${table}">
                        <input name="tableId" type="hidden" th:field="*{tableId}" />
                        <div class="tab-content">
                            <!-- 基本信息 -->
                            <div class="tab-pane" id="tab-basic">
				            <div class="row mt20">
				            	<div class="col-sm-6">
				                    <div class="form-group">
				                        <label class="col-sm-4 control-label is-required">表名称：</label>
				                        <div class="col-sm-8">
				                            <input name="tableName" class="form-control" type="text" placeholder="请输入表名称" maxlength="200" th:field="*{tableName}" required>
				                        </div>
				                    </div>
				                </div>
				                <div class="col-sm-6">
				                    <div class="form-group">
				                        <label class="col-sm-4 control-label is-required">表描述：</label>
				                        <div class="col-sm-8">
				                            <input name="tableComment" class="form-control" type="text" placeholder="请输入表描述" maxlength="500" th:field="*{tableComment}" required>
				                        </div>
				                    </div>
				                </div>
				            </div>
				            <div class="row">
				                <div class="col-sm-6">
				                    <div class="form-group">
				                        <label class="col-sm-4 control-label is-required">实体类名称：</label>
				                        <div class="col-sm-8">
				                            <input name="className" class="form-control" type="text" placeholder="请输入实体类名称" maxlength="100" th:field="*{className}" required>
				                        </div>
				                    </div>
				                </div>
				                <div class="col-sm-6">
				                    <div class="form-group">
				                        <label class="col-sm-4 control-label is-required">作者：</label>
				                        <div class="col-sm-8">
				                            <input name="functionAuthor" class="form-control" type="text" placeholder="请输入作者" maxlength="50" th:field="*{functionAuthor}" required>
				                        </div>
				                    </div>
				                </div>
				            </div>
				            <div class="row">
				                <div class="col-sm-12">
				                    <div class="form-group">
				                        <label class="col-xs-2 control-label">备注：</label>
				                        <div class="col-xs-10">
				                            <textarea name="remark" maxlength="500" class="form-control" rows="3"></textarea>
				                        </div>
				                    </div>
				                </div>
				            </div>
                            </div>
                            
                            <!-- 字段信息 -->
                            <div class="tab-pane active" id="tab-field">
                                <div class="select-table table-striped" style="margin-top: 0px;padding-top: 0px;padding-bottom: 0px;">
								    <table id="bootstrap-table" data-use-row-attr-func="true" data-reorderable-rows="true"></table>
								</div>
                            </div>
                            
                            <!-- 生成信息 -->
                            <div class="tab-pane" id="tab-gen">
					            <div class="row mt20">
					            	<div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required">生成模板：</label>
					                        <div class="col-sm-8">
					                            <select class='form-control' id="tplCategory" name='tplCategory' style="width: 100%">
												    <option value="crud" th:field="*{tplCategory}">单表（增删改查）</option>
												    <option value="tree" th:field="*{tplCategory}">树表（增删改查）</option>
												    <option value="sub" th:field="*{tplCategory}">主子表（增删改查）</option>
												</select>
					                        </div>
					                    </div>
					                </div>
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="生成在哪个java包下，例如 com.lirong.project.system">生成包路径：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <input name="packageName" class="form-control" type="text" placeholder="请输入生成包路径" maxlength="100" th:field="*{packageName}" required>
					                        </div>
					                    </div>
					                </div>
					            </div>
					            <div class="row">
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="可理解为子系统名，例如 system">生成模块名：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <input name="moduleName" class="form-control" type="text" placeholder="请输入生成模块名" maxlength="30" th:field="*{moduleName}" required>
					                        </div>
					                    </div>
					                </div>
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="可理解为功能英文名，例如 user">生成业务名：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <input name="businessName" class="form-control" type="text" placeholder="请输入生成业务名" maxlength="50" th:field="*{businessName}" required>
					                        </div>
					                    </div>
					                </div>
					            </div>
					            <div class="row">
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="用作类描述，例如 用户">生成功能名：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <input name="functionName" class="form-control" type="text" placeholder="请输入生成功能名" maxlength="50" th:field="*{functionName}" required>
					                        </div>
					                    </div>
					                </div>
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="分配到指定菜单下，例如 系统管理">上级菜单：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <input id="parentMenuId" name="params[parentMenuId]" type="hidden" th:value="*{parentMenuId}"/>
					                            <div class="input-group">
												    <input id="parentMenuName" name="params[parentMenuName]" class="form-control" type="text" onclick="selectMenuTree()" placeholder="请选择上级菜单" maxlength="50" th:value="*{parentMenuName}" required>
											        <span class="input-group-addon"><i class="fa fa-search"></i></span>
											    </div>
					                        </div>
					                    </div>
					                </div>
					            </div>
					            <div class="row">
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label" title="默认为zip压缩包下载，也可以自定义生成路径">生成代码方式：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
                                                <label class="radio-box"> <input type="radio" name="genType" value="0" th:field="*{genType}" /> zip压缩包 </label> 
                                                <label class="radio-box"> <input type="radio" name="genType" value="1" th:field="*{genType}" /> 自定义路径</label> 
					                        </div>
					                    </div>
					                </div>
					            </div>
					            <div class="hidden row" id="pathinfo">
					                <div class="col-sm-12">
					                    <div class="form-group">
					                        <label class="col-xs-2 control-label" title="填写磁盘绝对路径，若不填写，则生成到当前Web项目下">生成基础路径：<i class="fa fa-question-circle-o"></i></label>
						                    <div class="col-xs-10">
						                        <div class="input-group input-group-sm">
						                            <input id="genPath" name="genPath" class="form-control" type="text" th:field="*{genPath}" placeholder="请输入项目路径" maxlength="200">
						                            <div class="input-group-btn">
						                               <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown">最近路径快速选择 <span class="caret"></span></button>
															<ul class="dropdown-menu dropdown-menu-right" role="menu">
																<li><a href="javascript:$('#genPath').val('/')"><i class="fa fa-refresh"></i>恢复默认的生成基础路径</a></li>
															</ul>
														</div>
													</div>
						                    </div>
					                    </div>
					                </div>
					            </div>
					            <div class="hidden" id="subInfo">
					            <h4 class="form-header h4">关联信息</h4>
					            <div class="row">
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="关联子表的表名， 如：sys_user">关联子表的表名：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <select class='type form-control' id="subTableName" name='subTableName' th:attr='data-value=*{subTableName}' style="width: 100%">
					                                <option value="">---请选择---</option>
												</select>
					                        </div>
					                    </div>
					                </div>
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="子表关联的外键名， 如：user_id">子表关联的外键名：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <select class='router form-control' id="subTableFkName" name='subTableFkName' th:attr='data-value=*{subTableFkName}' style="width: 100%">
					                                <option value="">---请选择---</option>
												</select>
					                        </div>
					                    </div>
					                </div>
					            </div>
					            </div>
					            <div class="hidden" id="otherInfo">
					            <h4 class="form-header h4">其他信息</h4>
					            <div class="row">
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="树显示的编码字段名， 如：dept_id">树编码字段：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <select class='form-control' id="treeCode" name='params[treeCode]' style="width: 100%">
					                                <option value="">---请选择---</option>
												    <option th:each="column : ${table.columns}" th:text="${column.columnName + '：' + column.columnComment}" th:value="${column.columnName}" th:field="*{treeCode}"></option>
												</select>
					                        </div>
					                    </div>
					                </div>
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="树显示的父编码字段名， 如：parent_Id">树父编码字段：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <select class='form-control' id="treeParentCode" name='params[treeParentCode]' style="width: 100%">
												    <option value="">---请选择---</option>
												    <option th:each="column : ${table.columns}" th:text="${column.columnName + '：' + column.columnComment}" th:value="${column.columnName}" th:field="*{treeParentCode}"></option>
												</select>
					                        </div>
					                    </div>
					                </div>
					            </div>
					            <div class="row">
					                <div class="col-sm-6">
					                    <div class="form-group">
					                        <label class="col-sm-4 control-label is-required" title="树节点的显示名称字段名， 如：dept_name">树名称字段：<i class="fa fa-question-circle-o"></i></label>
					                        <div class="col-sm-8">
					                            <select class='form-control' id="treeName" name='params[treeName]' style="width: 100%">
												    <option value="">---请选择---</option>
												    <option th:each="column : ${table.columns}" th:text="${column.columnName + '：' + column.columnComment}" th:value="${column.columnName}" th:field="*{treeName}"></option>
												</select>
					                        </div>
					                    </div>
					                </div>
					            </div>
					            </div>
                            </div>
                        </div>
                        </form>
                    </div>
                </div>
               <div class="box-footer">
			       <div class="col-sm-offset-5 col-sm-6">
			           <button type="button" class="btn btn-sm btn-primary" onclick="submitHandler()"><i class="fa fa-check"></i>保 存</button>&nbsp;
			           <button type="button" class="btn btn-sm btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭 </button>
			       </div>
           	   </div>
            </div>
        </div>
    </div>
	</section>
    <th:block th:include="include :: footer" />
    <th:block th:include="include :: select2-js" />
    <th:block th:include="include :: bootstrap-table-reorder-js" />
    <script th:src="@{/js/jquery.tmpl.js}"></script>
    <th:block th:include="include :: jquery-cxselect-js" />
    <script th:inline="javascript">
	    /* 用户信息-修改 */
	    $("#form-table-edit").validate({
	    	rules: {
	    	    tableName: {
	    	        required: true,
	    	    },
	    	},
			focusCleanup: true
		});
	    
	    /* 表级联信息 */
   		var data = [[${data}]];
    	$('#subInfo').cxSelect({
    	  selects: ['type', 'router'],
    	  jsonValue: 'v',
    	  data: data
    	});
	    
	    function submitHandler() {
	        if ($.validate.form()) {
	        	$.operate.saveTab(prefix + "/edit", $("#form-gen-edit").serializeArray());
	        }
	    }
	    
	    var prefix = ctx + "tool/gen";
		$(function() {
		    var options = {
		        url: prefix + "/column/list",
		        sortName: "sort",
		        sortOrder: "desc",
		        height: $(window).height() - 166,
		        pagination: false,
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        onLoadSuccess: onLoadSuccess,
		        onReorderRow: onReorderRow,
		        columns: [{
                    title: "序号",
                    width: "5%",
                    formatter: function (value, row, index) {
                    	// 编号隐藏域
                    	var columnIdHtml = $.common.sprintf("<input type='hidden' name='columns[%s].columnId' value='%s'>", index, row.columnId);
                    	// 排序隐藏域
                    	var sortHtml = $.common.sprintf("<input type='hidden' name='columns[%s].sort' value='%s' id='columns_sort_%s'>", index, row.sort, row.columnId);
                    	return columnIdHtml + sortHtml + $.table.serialNumber(index);
                    },
                    cellStyle: function(value, row, index) {
                        return { css: { "cursor": "move" } };
                    }
                },
		        {
		            field: 'columnName',
		            title: '字段列名',
		            width: "10%",
		            class: "nodrag",
		            cellStyle: function(value, row, index) {
                        return { css: { "cursor": "default" } };
                    }
		        },
		        {
		            field: 'columnComment',
		            title: '字段描述',
		            width: "10%",
		            formatter: function (value, row, index) {
		            	var html = $.common.sprintf("<input class='form-control' type='text' name='columns[%s].columnComment' value='%s'>", index, value);
		        		return html;
		        	}
		        },
		        {
		            field: 'columnType',
		            title: '物理类型',
		            width: "10%",
		            class: "nodrag",
		            cellStyle: function(value, row, index) {
                        return { css: { "cursor": "default" } };
                    }
		        },
		        {
		            field: 'javaType',
		            title: 'Java类型',
		            width: "10%",
		            formatter: function (value, row, index) {
		        		var data = [{ index: index, javaType: value }];
		                return $("#javaTypeTpl").tmpl(data).html();
		        	}
		        },
		        {
		            field: 'javaField',
		            title: 'Java属性',
		            width: "10%",
		            formatter: function (value, row, index) {
		        		var html = $.common.sprintf("<input class='form-control' type='text' name='columns[%s].javaField' value='%s' required>", index, value);
		        		return html;
		        	}
		        },
		        {
		            field: 'isInsert',
		            title: '插入',
		            width: "5%",
		            formatter: function (value, row, index) {
		            	var isCheck = value == 1 ? 'checked' : '';
		            	var html = $.common.sprintf("<label class='check-box'><input type='checkbox' name='columns[%s].isInsert' value='1' %s></label>", index, isCheck);
		        		return html;
		        	}
		        },
		        {
		            field: 'isEdit',
		            title: '修改',
		            width: "5%",
		            formatter: function (value, row, index) {
		            	var isCheck = value == 1 ? 'checked' : '';
		            	var html = $.common.sprintf("<label class='check-box'><input type='checkbox' name='columns[%s].isEdit' value='1' %s></label>", index, isCheck);
		        		return html;
		        	}
		        },
		        {
		            field: 'isList',
		            title: '列表',
		            width: "5%",
		            formatter: function (value, row, index) {
		            	var isCheck = value == 1 ? 'checked' : '';
		            	var html = $.common.sprintf("<label class='check-box'><input type='checkbox' name='columns[%s].isList' value='1' %s></label>", index, isCheck);
		        		return html;
		        	}
		        },
		        {
		            field: 'isQuery',
		            title: '查询',
		            width: "5%",
		            formatter: function (value, row, index) {
		            	var isCheck = value == 1 ? 'checked' : '';
		            	var html = $.common.sprintf("<label class='check-box'><input type='checkbox' name='columns[%s].isQuery' value='1' %s></label>", index, isCheck);
		        		return html;
		        	}
		        },
		        {
		            field: 'queryType',
		            title: '查询方式',
		            width: "10%",
		            formatter: function (value, row, index) {
		            	var data = [{ index: index, queryType: value }];
		                return $("#queryTypeTpl").tmpl(data).html();
		        	}
		        },
		        {
		            field: 'isRequired',
		            title: '必填',
		            width: "5%",
		            formatter: function (value, row, index) {
		            	var isCheck = value == 1 ? 'checked' : '';
		            	var html = $.common.sprintf("<label class='check-box'><input type='checkbox' name='columns[%s].isRequired' value='1' %s></label>", index, isCheck);
		        		return html;
		        	}
		        },
		        {
		            field: 'htmlType',
		            title: '显示类型',
		            width: "12%",
		            formatter: function (value, row, index) {
		            	var data = [{ index: index, htmlType: value }];
		                return $("#htmlTypeTpl").tmpl(data).html();
		        	}
		        },
		        {
		            field: 'dictType',
		            title: '字典类型',
		            width: "13%",
		            formatter: function (value, row, index) {
		        		var html = $.common.sprintf("<input class='form-control' type='text' name='columns[%s].dictType' value='%s' id='columns_dict_%s'>", index, value, row.columnId);
		        		return "<div class='input-group'>" + html + "<span class='input-group-addon input-sm' onclick='selectDictTree(" + row.columnId + ", this)'><i class='fa fa-search'></i></span></div>";
		        	},
		        	cellStyle: function(value, row, index) {
                        return { css: { "cursor": "default" } };
                    }
		        }]
		    };
		    $.table.init(options);
		});
		
		// 当所有数据被加载时触发处理函数
		function onLoadSuccess(data){
			$.fn.select2.defaults.set( "theme", "bootstrap" );
			$("select.form-control").each(function () {
				$(this).select2().on("change", function () {
					$(this).valid();
				})
			})
			$(".check-box").each(function() {
	            $(this).iCheck({
	                checkboxClass: 'icheckbox-blue'
            	})
        	})
        }
		
		// 当拖拽结束后处理函数
		function onReorderRow(data) {
	        for (var i = 0; i < data.length; i++) {
			    $("#columns_sort_" + data[i].columnId).val(i+1);
			}
       }
		
		$(function() {
            var tplCategory = $("#tplCategory option:selected").val();
            tplCategoryVisible(tplCategory);
            var genType = $('input[name="genType"]:checked').val();
            pathInfoVisible(genType);
        });
		
		$('#tplCategory').on('select2:select', function (event) {
			var tplCategory = $(event.target).val();
			tplCategoryVisible(tplCategory);
		});
		
		function tplCategoryVisible(tplCategory) {
			if("crud" == tplCategory){
				$("#treeCode").select2("val", [""]);
				$("#treeParentCode").select2("val", [""]);
				$("#treeName").select2("val", [""]);
				$("#otherInfo").addClass("hidden");
				$("#subInfo").addClass("hidden");
			} else if("tree" == tplCategory){
				$("#otherInfo").removeClass("hidden");
				$("#treeCode").attr("required", "true");
				$("#treeParentCode").attr("required", "true");
				$("#treeName").attr("required", "true");
				$("#subInfo").addClass("hidden");
			} else if("sub" == tplCategory){
				$("#subInfo").removeClass("hidden");
				$("#treeCode").select2("val", [""]);
				$("#treeParentCode").select2("val", [""]);
				$("#treeName").select2("val", [""]);
				$("#subTableName").attr("required", "true");
				$("#subTableFkName").attr("required", "true");
				$("#otherInfo").addClass("hidden");
			}
        }
		
		$('input').on('ifChecked', function(event){
			var genType = $(event.target).val();
			pathInfoVisible(genType);
		});
		
		function pathInfoVisible(genType) {
			if("0" == genType){
				$("#genPath").val("/");
				$("#pathinfo").addClass("hidden");
			} else if("1" == genType){
				$("#pathinfo").removeClass("hidden");
			}
		}
		
		// 选择字典处理函数
		function selectDictTree(columnId, obj) {
			var dictType = $.common.nullToStr($(obj).parent().find("input").val());
        	var url = ctx + "system/dict/selectDictTree/" + columnId + "/" + dictType;
			var options = {
				title: '选择字典类型',
				width: "380",
				url: url,
				callBack: doDictSubmit
			};
			$.modal.openOptions(options);
		}
		
		// 选择菜单处理函数
        function selectMenuTree() {
        	var parentMenuId = $("#parentMenuId").val();
        	var menuId = parentMenuId > 0 ? parentMenuId : 1;
        	var url = ctx + "system/menu/selectMenuTree/" + menuId;
			var options = {
				title: '菜单选择',
				width: "380",
				url: url,
				callBack: doMenuSubmit
			};
			$.modal.openOptions(options);
		}
		
		function doDictSubmit(index, layero){
			var body = layer.getChildFrame('body', index);
   			var columnId = body.find('#columnId').val();
   			var dictType = body.find('#dictType').val();
   			layer.close(index);
   			$("#columns_dict_" + columnId).val(dictType);
		}
		
		function doMenuSubmit(index, layero){
			var body = layer.getChildFrame('body', index);
   			$("#parentMenuId").val(body.find('#treeId').val());
   			$("#parentMenuName").val(body.find('#treeName').val());
   			layer.close(index);
		}
    </script>
</body>
</html>


<!-- java类型 -->
<script id="javaTypeTpl" type="text/x-jquery-tmpl">
<div>
<select class='form-control' name='columns[${index}].javaType'>
    <option value="Long" {{if javaType==="Long"}}selected{{/if}}>Long</option>
    <option value="String" {{if javaType==="String"}}selected{{/if}}>String</option>
    <option value="Integer" {{if javaType==="Integer"}}selected{{/if}}>Integer</option>
    <option value="Double" {{if javaType==="Double"}}selected{{/if}}>Double</option>
    <option value="BigDecimal" {{if javaType==="BigDecimal"}}selected{{/if}}>BigDecimal</option>
    <option value="Date" {{if javaType==="Date"}}selected{{/if}}>Date</option>
</select>
</div>
</script>

<!-- 查询方式 -->
<script id="queryTypeTpl" type="text/x-jquery-tmpl">
<div>
<select class='form-control' name='columns[${index}].queryType'>
    <option value="EQ" {{if queryType==="EQ"}}selected{{/if}}>=</option>
    <option value="NE" {{if queryType==="NE"}}selected{{/if}}>!=</option>
    <option value="GT" {{if queryType==="GT"}}selected{{/if}}>></option>
    <option value="GTE" {{if queryType==="GTE"}}selected{{/if}}>>=</option>
    <option value="LT" {{if queryType==="LT"}}selected{{/if}}><</option>
    <option value="LTE" {{if queryType==="LTE"}}selected{{/if}}><=</option>
    <option value="LIKE" {{if queryType==="LIKE"}}selected{{/if}}>Like</option>
    <option value="BETWEEN" {{if queryType==="BETWEEN"}}selected{{/if}}>Between</option>
</select>
</div>
</script>

<!-- 显示类型 -->
<script id="htmlTypeTpl" type="text/x-jquery-tmpl">
<div>
<select class='form-control' name='columns[${index}].htmlType'>
    <option value="input" {{if htmlType==="input"}}selected{{/if}}>文本框</option>
    <option value="textarea" {{if htmlType==="textarea"}}selected{{/if}}>文本域</option>
    <option value="select" {{if htmlType==="select"}}selected{{/if}}>下拉框</option>
    <option value="radio" {{if htmlType==="radio"}}selected{{/if}}>单选框</option>
    <option value="checkbox" {{if htmlType==="checkbox"}}selected{{/if}}>复选框</option>
    <option value="summernote" {{if htmlType==="summernote"}}selected{{/if}}>富文本</option>
    <option value="datetime" {{if htmlType==="datetime"}}selected{{/if}}>日期控件</option>
    <option value="upload" {{if htmlType==="upload"}}selected{{/if}}>上传控件</option>
</select>
</div>
</script>
