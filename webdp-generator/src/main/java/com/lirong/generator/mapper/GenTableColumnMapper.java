package com.lirong.generator.mapper;

import java.util.List;

import com.lirong.generator.domain.GenTableColumn;

/**
 * 业务字段 数据层
 *
 * <AUTHOR>
 */
public interface GenTableColumnMapper {
    /**
     * 根据表名称查询列信息
     *
     * @param tableName 表名称
     * @return 列信息
     */
    public List<GenTableColumn> selectDbTableColumnsByName(String tableName);

    /**
     * 查询业务字段列表
     *
     * @param genTableColumn 业务字段信息
     * @return 业务字段集合
     */
    public List<GenTableColumn> selectGenTableColumnListByTableId(GenTableColumn genTableColumn);

    /**
     * 新增业务字段
     *
     * @param genTableColumn 业务字段信息
     * @return 结果
     */
    public int insertGenTableColumn(GenTableColumn genTableColumn);

    /**
     * 修改业务字段
     *
     * @param genTableColumn 业务字段信息
     * @return 结果
     */
    public int updateGenTableColumn(GenTableColumn genTableColumn);

    /**
     * 删除业务字段
     *
     * @param genTableColumns 列数据
     * @return 结果
     */
    public int deleteGenTableColumns(List<GenTableColumn> genTableColumns);

    /**
     * 批量删除业务字段
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteGenTableColumnByIds(Long[] ids);

    /**
     * 根据表名获取简易字段信息
     *
     * @param tableName 数据库表名
     * @return 结果
     */
    public List<GenTableColumn> selectSimpleDbTableColumnsByName(String tableName);

    /**
     * 查询数据库属性列表
     *
     * @param genTableColumn 数据库属性
     * @return 结果
     */
    public List<GenTableColumn> selectDbTableColumnsList(GenTableColumn genTableColumn);
}
