
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-religious">
                    <input type="hidden" name="peopleCode" th:value="${peopleCode}">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>宗教身份：</label>
                                <input type="text" name="religiousIdentity"/>
                            </li>
                            <li>
                                <label>所属教派：</label>
                                <input type="text" name="religiousSect"/>
                            </li>
                            <li>
                                <label>所属寺庙：</label>
                                <input type="text" name="temple"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-religious', 'bootstrap-table-religious')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-religious', 'bootstrap-table-religious')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-religious" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="people:religious:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="people:religious:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="people:religious:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
                <!--<a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="people:religious:export">
                    <i class="fa fa-download"></i> 导出
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-religious"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        let editFlag = [[${@permission.hasPermi('people:religious:edit')}]];
        let removeFlag = [[${@permission.hasPermi('people:religious:remove')}]];
        let prefix = ctx + "people/religious";

        $(function() {
            let options = {
                id: "bootstrap-table-religious",          // 指定表格ID
                toolbar: "toolbar-religious",   // 指定工具栏ID
                formId: "form-religious",
                url: prefix + "/list",
                createUrl: prefix + "/add/"+peopleCode,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "人员宗教信息",
                uniqueId: "religiousId",
                columns: [{
                    checkbox: true
                },
                {
                    align: 'center',
                    title: "序号",
                    width: 60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'religiousBelief',
                    title: '宗教信仰'
                },
                {
                    field: 'religiousIdentity',
                    title: '宗教身份'
                },
                {
                    field: 'religiousSect',
                    title: '所属教派'
                },
                {
                    field: 'temple',
                    title: '所属寺庙'
                },
                {
                    field: 'joinTempleDate',
                    title: '入寺时间'
                },
                {
                    field: 'dharmaName',
                    title: '道名/法名'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.religiousId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.religiousId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
