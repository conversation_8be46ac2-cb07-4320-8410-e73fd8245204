package com.lirong.personnel.scientist.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;

import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.math.BigDecimal;

/**
 * 装备领域科学家对象 idw_people_main
 *
 * <AUTHOR>
 * @date 2020-12-28
 */
public class IdwScientistMain extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 人员ID
     */
    private Long peopleId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码")
    private String peopleCode;

    /**
     * 国家
     */
    @Excel(name = "国家", dictType = "sys_country")
    private String country;

    /**
     * 人员所属类型
     */
    @Excel(name = "人员所属类型", dictType = "personnel_scientist_type")
    private String peopleType;

    /**
     * 人员分类
     */
    private String category;

    /**
     * 中文名称
     */
    @Excel(name = "中文名称")
    private String nameCn;

    /**
     * 英文名称
     */
    @Excel(name = "英文名称")
    private String nameEn;

    /**
     * 工作状态
     */
    @Excel(name = "工作状态", dictType = "sys_work_status", combo = {"现任", "离任"})
    private String status;

    /**
     * 性别
     */
    @Excel(name = "性别", dictType = "sys_user_sex", combo = {"男", "女", "未知"})
    private String gender;

    /**
     * 出生地
     */
    @Excel(name = "出生地")
    private String birthplace;

    /**
     * 出生日期
     */
    @Excel(name = "出生日期")
    private String birthday;

    private String birthdayYear;
    private String birthdayMonth;
    private String birthdayDay;

    /**
     * 年龄
     */
    @Excel(name = "年龄")
    private Integer age;

    /**
     * 头像
     */
    @Excel(name = "头像")
    private String avatar;

    /**
     * 邮箱
     */
    @Excel(name = "邮箱")
    private String email;

    /**
     * 联系方式
     */
    @Excel(name = "联系方式|手机号码")
    private String telephone;

    /**
     * 毕业院校
     */
    @Excel(name = "毕业院校")
    private String graduatedUniversity;

    /**
     * 学位
     */
    @Excel(name = "学位")
    private String degree;

    /**
     * 学历
     */
    @Excel(name = "学历")
    private String education;

    /**
     * 所在机构编码
     */
    @Excel(name = "所在机构编码")
    private String orgCode;

    /**
     * 所在机构名称
     */
    @Excel(name = "所在机构名称")
    private String orgName;

    /**
     * 当前职务/岗位
     */
    @Excel(name = "当前职务")
    private String post;

    /**
     * 任职日期
     */
    @Excel(name = "任职日期")
    private String appointmentDate;

    private String appointmentYear;
    private String appointmentMonth;
    private String appointmentDay;

    /**
     * 职业
     */
    @Excel(name = "职业")
    private String occupation;

    /**
     * 工作地点
     */
    @Excel(name = "工作地点")
    private String workplace;

    /**
     * 参与的组织
     */
    @Excel(name = "参与的组织")
    private String participantOrg;

    /**
     * 内网编码
     */
    @Excel(name = "内网编码")
    private String innerCode;

    /**
     * 中文简介
     */
    @Excel(name = "中文简介")
    private String profileCn;

    /**
     * 英文简介
     */
    @Excel(name = "英文简介")
    private String profileEn;

    /**
     * 教育经历
     */
    @Excel(name = "教育经历")
    private String educationalExperience;

    /**
     * 工作经历
     */
    @Excel(name = "工作经历")
    private String assignments;

    /**
     * 性格特点
     */
    @Excel(name = "性格特点")
    private String peopleCharacter;

    /**
     * 兴趣爱好
     */
    @Excel(name = "兴趣爱好")
    private String hobby;

    /**
     * 强项弱项/强弱项
     */
    @Excel(name = "强项弱项")
    private String strengthsWeaknesses;

    /**
     * 技术特长
     */
    @Excel(name = "技术特长")
    private String technicalExpertise;

    /**
     * 主要成就
     */
    @Excel(name = "主要成就")
    private String achievement;

    /**
     * 身体状况
     */
    @Excel(name = "身体状况")
    private String physicalCondition;

    /**
     * 大五人格-外倾性
     */
    private BigDecimal extraversion;

    /**
     * 大五人格-神经质性
     */
    private BigDecimal emotionalStability;

    /**
     * 政治倾向性
     */
    @Excel(name = "政治倾向性", dictType = "sys_political_orientation")
    private String politicalOrientation;

    /**
     * 大五人格-宜人性
     */
    private BigDecimal agreeableness;

    /**
     * 大五人格-尽责性
     */
    private BigDecimal conscientiousness;

    /**
     * 大五人格-开放性
     */
    private BigDecimal openness;

    /**
     * 对华态度
     */
    @Excel(name = "对华态度", dictType = "sys_attitude_towards_china")
    private String attitudeTowardsChina;

    /**
     * 排序号
     */
    @Excel(name = "排序号")
    private Integer orderNum;

    /**
     * 标签
     */
    @Excel(name = "标签")
    private String tags;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    public Long getPeopleId() {
        return peopleId;
    }

    public void setPeopleId(Long peopleId) {
        this.peopleId = peopleId;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getCountry() {
        return StringUtils.isNotBlank(country) ? country.trim() : country;
    }

    public void setCountry(String country) {
        this.country = country;
    }

    public String getPeopleType() {
        return peopleType;
    }

    public void setPeopleType(String peopleType) {
        this.peopleType = peopleType;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getNameCn() {
        if (StringUtils.isNotBlank(nameCn)) {
            return nameCn.trim();
        } else {
            return nameCn;
        }
    }

    public void setNameCn(String nameCn) {
        this.nameCn = nameCn;
    }

    public String getNameEn() {
        if (StringUtils.isNotBlank(nameEn)) {
            return nameEn.trim();
        } else {
            return nameEn;
        }
    }

    public void setNameEn(String nameEn) {
        this.nameEn = nameEn;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getBirthplace() {
        return birthplace;
    }

    public void setBirthplace(String birthplace) {
        this.birthplace = birthplace;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getBirthdayYear() {
        return birthdayYear;
    }

    public void setBirthdayYear(String birthdayYear) {
        this.birthdayYear = birthdayYear;
    }

    public String getBirthdayMonth() {
        return birthdayMonth;
    }

    public void setBirthdayMonth(String birthdayMonth) {
        this.birthdayMonth = birthdayMonth;
    }

    public String getBirthdayDay() {
        return birthdayDay;
    }

    public void setBirthdayDay(String birthdayDay) {
        this.birthdayDay = birthdayDay;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getTelephone() {
        return telephone;
    }

    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }

    public String getGraduatedUniversity() {
        return graduatedUniversity;
    }

    public void setGraduatedUniversity(String graduatedUniversity) {
        this.graduatedUniversity = graduatedUniversity;
    }

    public String getDegree() {
        return degree;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }


    public String getEducation() {
        return education;
    }

    public void setEducation(String education) {
        this.education = education;
    }

    public String getOrgCode() {
        return orgCode;
    }

    public void setOrgCode(String orgCode) {
        this.orgCode = orgCode;
    }

    public String getOrgName() {
        return orgName;
    }

    public void setOrgName(String orgName) {
        this.orgName = orgName;
    }

    public String getPost() {
        return post;
    }

    public void setPost(String post) {
        this.post = post;
    }

    public String getAppointmentDate() {
        return appointmentDate;
    }

    public void setAppointmentDate(String appointmentDate) {
        this.appointmentDate = appointmentDate;
    }

    public String getAppointmentYear() {
        return appointmentYear;
    }

    public void setAppointmentYear(String appointmentYear) {
        this.appointmentYear = appointmentYear;
    }

    public String getAppointmentMonth() {
        return appointmentMonth;
    }

    public void setAppointmentMonth(String appointmentMonth) {
        this.appointmentMonth = appointmentMonth;
    }

    public String getAppointmentDay() {
        return appointmentDay;
    }

    public void setAppointmentDay(String appointmentDay) {
        this.appointmentDay = appointmentDay;
    }

    public String getOccupation() {
        return occupation;
    }

    public void setOccupation(String occupation) {
        this.occupation = occupation;
    }

    public String getWorkplace() {
        return workplace;
    }

    public void setWorkplace(String workplace) {
        this.workplace = workplace;
    }

    public String getParticipantOrg() {
        return participantOrg;
    }

    public void setParticipantOrg(String participantOrg) {
        this.participantOrg = participantOrg;
    }

    public String getInnerCode() {
        return innerCode;
    }

    public void setInnerCode(String innerCode) {
        this.innerCode = innerCode;
    }

    public String getProfileCn() {
        return profileCn;
    }

    public void setProfileCn(String profileCn) {
        this.profileCn = profileCn;
    }

    public String getProfileEn() {
        return profileEn;
    }

    public void setProfileEn(String profileEn) {
        this.profileEn = profileEn;
    }

    public String getEducationalExperience() {
        return educationalExperience;
    }

    public void setEducationalExperience(String educationalExperience) {
        this.educationalExperience = educationalExperience;
    }

    public String getAssignments() {
        return assignments;
    }

    public void setAssignments(String assignments) {
        this.assignments = assignments;
    }

    public String getPeopleCharacter() {
        return peopleCharacter;
    }

    public void setPeopleCharacter(String peopleCharacter) {
        this.peopleCharacter = peopleCharacter;
    }

    public String getHobby() {
        return hobby;
    }

    public void setHobby(String hobby) {
        this.hobby = hobby;
    }

    public String getStrengthsWeaknesses() {
        return strengthsWeaknesses;
    }

    public void setStrengthsWeaknesses(String strengthsWeaknesses) {
        this.strengthsWeaknesses = strengthsWeaknesses;
    }

    public String getTechnicalExpertise() {
        return technicalExpertise;
    }

    public void setTechnicalExpertise(String technicalExpertise) {
        this.technicalExpertise = technicalExpertise;
    }

    public String getAchievement() {
        return achievement;
    }

    public void setAchievement(String achievement) {
        this.achievement = achievement;
    }

    public String getPhysicalCondition() {
        return physicalCondition;
    }

    public void setPhysicalCondition(String physicalCondition) {
        this.physicalCondition = physicalCondition;
    }

    public BigDecimal getExtraversion() {
        return extraversion;
    }

    public void setExtraversion(BigDecimal extraversion) {
        this.extraversion = extraversion;
    }

    public BigDecimal getEmotionalStability() {
        return emotionalStability;
    }

    public void setEmotionalStability(BigDecimal emotionalStability) {
        this.emotionalStability = emotionalStability;
    }

    public String getPoliticalOrientation() {
        return politicalOrientation;
    }

    public void setPoliticalOrientation(String politicalOrientation) {
        this.politicalOrientation = politicalOrientation;
    }

    public BigDecimal getAgreeableness() {
        return agreeableness;
    }

    public void setAgreeableness(BigDecimal agreeableness) {
        this.agreeableness = agreeableness;
    }

    public BigDecimal getConscientiousness() {
        return conscientiousness;
    }

    public void setConscientiousness(BigDecimal conscientiousness) {
        this.conscientiousness = conscientiousness;
    }

    public BigDecimal getOpenness() {
        return openness;
    }

    public void setOpenness(BigDecimal openness) {
        this.openness = openness;
    }

    public String getAttitudeTowardsChina() {
        return attitudeTowardsChina;
    }

    public void setAttitudeTowardsChina(String attitudeTowardsChina) {
        this.attitudeTowardsChina = attitudeTowardsChina;
    }

    public Integer getOrderNum() {
        return orderNum;
    }

    public void setOrderNum(Integer orderNum) {
        this.orderNum = orderNum;
    }

    public String getTags() {
        return tags;
    }

    public void setTags(String tags) {
        this.tags = tags;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("peopleId", getPeopleId())
                .append("peopleCode", getPeopleCode())
                .append("country", getCountry())
                .append("peopleType", getPeopleType())
                .append("category", getCategory())
                .append("nameCn", getNameCn())
                .append("nameEn", getNameEn())
                .append("status", getStatus())
                .append("gender", getGender())
                .append("birthplace", getBirthplace())
                .append("birthday", getBirthday())
                .append("birthdayYear", getBirthdayYear())
                .append("birthdayMonth", getBirthdayMonth())
                .append("birthdayDay", getBirthdayDay())
                .append("age", getAge())
                .append("avatar", getAvatar())
                .append("email", getEmail())
                .append("telephone", getTelephone())
                .append("graduatedUniversity", getGraduatedUniversity())
                .append("degree", getDegree())
                .append("education", getEducation())
                .append("orgCode", getOrgCode())
                .append("orgName", getOrgName())
                .append("post", getPost())
                .append("appointmentDate", getAppointmentDate())
                .append("appointmentYear", getAppointmentYear())
                .append("appointmentMonth", getAppointmentMonth())
                .append("appointmentDay", getAppointmentDay())
                .append("occupation", getOccupation())
                .append("workplace", getWorkplace())
                .append("participantOrg", getParticipantOrg())
                .append("innerCode", getInnerCode())
                .append("profileCn", getProfileCn())
                .append("profileEn", getProfileEn())
                .append("educationalExperience", getEducationalExperience())
                .append("assignments", getAssignments())
                .append("peopleCharacter", getPeopleCharacter())
                .append("hobby", getHobby())
                .append("strengthsWeaknesses", getStrengthsWeaknesses())
                .append("technicalExpertise", getTechnicalExpertise())
                .append("achievement", getAchievement())
                .append("physicalCondition", getPhysicalCondition())
                .append("extraversion", getExtraversion())
                .append("emotionalStability", getEmotionalStability())
                .append("politicalOrientation", getPoliticalOrientation())
                .append("agreeableness", getAgreeableness())
                .append("conscientiousness", getConscientiousness())
                .append("openness", getOpenness())
                .append("attitudeTowardsChina", getAttitudeTowardsChina())
                .append("orderNum", getOrderNum())
                .append("tags", getTags())
                .append("isDelete", getIsDelete())
                .append("source", getSource())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
