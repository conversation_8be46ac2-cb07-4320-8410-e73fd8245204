package com.lirong.personnel.relationship.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 人员关系对象 idw_people_relationship
 *
 * <AUTHOR>
 * @date 2021-01-11
 */
public class IdwPeopleRelationship extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 人员关系ID
     */
    private Long relationshipId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码")
    private String peopleCode;

    /**
     * 关联人员编码
     */
    @Excel(name = "关联人员编码")
    private String relatedPeopleCode;

    /**
     * 关联人员名称
     */
    @Excel(name = "姓名")
    private String relatedPeopleName;

    /**
     * 关联人员头像
     */
    @Excel(name = "头像")
    private String relatedPeopleAvatar;

    /**
     * 关系
     */
    @Excel(name = "关系|称谓")
    private String relationship;

    /**
     * 出生年月
     */
    @Excel(name = "出生年月")
    private String birthDate;

    private String year;
    private String month;
    private String day;

    /**
     * 政治面貌
     */
    @Excel(name = "政治面貌")
    private String politicalStatus;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 工作单位及职务
     */
    @Excel(name = "工作单位及职务")
    private String remark;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    private Integer isDelete;

    public void setRelationshipId(Long relationshipId) {
        this.relationshipId = relationshipId;
    }

    public Long getRelationshipId() {
        return relationshipId;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public String getRelatedPeopleCode() {
        return relatedPeopleCode;
    }

    public void setRelatedPeopleCode(String relatedPeopleCode) {
        this.relatedPeopleCode = relatedPeopleCode;
    }

    public void setRelatedPeopleName(String relatedPeopleName) {
        this.relatedPeopleName = relatedPeopleName;
    }

    public String getRelatedPeopleName() {
        return relatedPeopleName;
    }

    public void setRelatedPeopleAvatar(String relatedPeopleAvatar) {
        this.relatedPeopleAvatar = relatedPeopleAvatar;
    }

    public String getRelatedPeopleAvatar() {
        return relatedPeopleAvatar;
    }

    public void setRelationship(String relationship) {
        this.relationship = relationship;
    }

    public String getRelationship() {
        return relationship;
    }

    public String getBirthDate() {
        return birthDate;
    }

    public void setBirthDate(String birthDate) {
        this.birthDate = birthDate;
    }

    public String getYear() {
        return year;
    }

    public void setYear(String year) {
        this.year = year;
    }

    public String getMonth() {
        return month;
    }

    public void setMonth(String month) {
        this.month = month;
    }

    public String getDay() {
        return day;
    }

    public void setDay(String day) {
        this.day = day;
    }

    public String getPoliticalStatus() {
        return politicalStatus;
    }

    public void setPoliticalStatus(String politicalStatus) {
        this.politicalStatus = politicalStatus;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    @Override
    public String getRemark() {
        return remark;
    }

    @Override
    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("relationshipId", getRelationshipId())
                .append("peopleCode", getPeopleCode())
                .append("relatedPeopleCode", getRelatedPeopleCode())
                .append("relatedPeopleName", getRelatedPeopleName())
                .append("relatedPeopleAvatar", getRelatedPeopleAvatar())
                .append("relationship", getRelationship())
                .append("birthDate", getBirthDate())
                .append("year", getYear())
                .append("month", getMonth())
                .append("day", getDay())
                .append("politicalStatus", getPoliticalStatus())
                .append("remark", getRemark())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
