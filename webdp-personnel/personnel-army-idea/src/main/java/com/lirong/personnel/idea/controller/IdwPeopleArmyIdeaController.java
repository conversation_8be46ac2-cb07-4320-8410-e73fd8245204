package com.lirong.personnel.idea.controller;

import java.util.List;
import java.util.Map;

import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.StringUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.personnel.idea.domain.IdwPeopleArmyIdea;
import com.lirong.personnel.idea.service.IdwPeopleArmyIdeaService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 治军理念Controller
 *
 * <AUTHOR>
 * @date 2021-04-06
 */
@Controller
@RequestMapping("/people/armyIdea")
public class IdwPeopleArmyIdeaController extends BaseController {
    private String prefix = "personnel/armyIdea";

    @Autowired
    private IdwPeopleArmyIdeaService idwPeopleArmyIdeaService;

    @RequiresPermissions("personnel:armyIdea:view")
    @GetMapping("/{peopleCode}")
    public String armyIdea(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/armyIdea";
    }

    /**
     * 查询治军理念列表
     */
    @RequiresPermissions("personnel:armyIdea:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        startPage();
        List<IdwPeopleArmyIdea> list = idwPeopleArmyIdeaService.selectIdwPeopleArmyIdeaList(idwPeopleArmyIdea);
        return getDataTable(list);
    }

    /**
     * 新增治军理念
     */
    @GetMapping("/add/{peopleCode}")
    public String add(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/add";
    }

    /**
     * 新增保存治军理念
     */
    @RequiresPermissions("personnel:armyIdea:add")
    @Log(title = "治军理念", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        if (StringUtils.isNotEmpty(idwPeopleArmyIdea.getDay())) {
            idwPeopleArmyIdea.setIssueDate(idwPeopleArmyIdea.getYear() + "-" + idwPeopleArmyIdea.getMonth() + "-" + idwPeopleArmyIdea.getDay());
        } else if (StringUtils.isNotEmpty(idwPeopleArmyIdea.getMonth())) {
            idwPeopleArmyIdea.setIssueDate(idwPeopleArmyIdea.getYear() + "-" + idwPeopleArmyIdea.getMonth());
        } else if (StringUtils.isNotEmpty(idwPeopleArmyIdea.getYear())) {
            idwPeopleArmyIdea.setIssueDate(idwPeopleArmyIdea.getYear());
        }
        return toAjax(idwPeopleArmyIdeaService.insertIdwPeopleArmyIdea(idwPeopleArmyIdea));
    }

    /**
     * 修改治军理念
     */
    @GetMapping("/edit/{ideaId}")
    public String edit(@PathVariable("ideaId") Long ideaId, ModelMap mmap) {
        IdwPeopleArmyIdea idwPeopleArmyIdea = idwPeopleArmyIdeaService.selectIdwPeopleArmyIdeaById(ideaId);
        if (StringUtils.isNotBlank(idwPeopleArmyIdea.getIssueDate())) {
            Map<String, String> dateMap = DateUtils.splitDate(idwPeopleArmyIdea.getIssueDate());
            idwPeopleArmyIdea.setYear(dateMap.get("year"));
            idwPeopleArmyIdea.setMonth(dateMap.get("month"));
            idwPeopleArmyIdea.setDay(dateMap.get("day"));
        }
        mmap.put("idwPeopleArmyIdea", idwPeopleArmyIdea);
        return prefix + "/edit";
    }

    /**
     * 修改保存治军理念
     */
    @RequiresPermissions("personnel:armyIdea:edit")
    @Log(title = "治军理念", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        if (StringUtils.isNotEmpty(idwPeopleArmyIdea.getDay())) {
            idwPeopleArmyIdea.setIssueDate(idwPeopleArmyIdea.getYear() + "-" + idwPeopleArmyIdea.getMonth() + "-" + idwPeopleArmyIdea.getDay());
        } else if (StringUtils.isNotEmpty(idwPeopleArmyIdea.getMonth())) {
            idwPeopleArmyIdea.setIssueDate(idwPeopleArmyIdea.getYear() + "-" + idwPeopleArmyIdea.getMonth());
        } else if (StringUtils.isNotEmpty(idwPeopleArmyIdea.getYear())) {
            idwPeopleArmyIdea.setIssueDate(idwPeopleArmyIdea.getYear());
        }
        return toAjax(idwPeopleArmyIdeaService.updateIdwPeopleArmyIdea(idwPeopleArmyIdea));
    }

    /**
     * 删除治军理念
     */
    @RequiresPermissions("personnel:armyIdea:remove")
    @Log(title = "治军理念", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(idwPeopleArmyIdeaService.deleteIdwPeopleArmyIdeaByIds(ids));
    }

    /**
     * 导出治军理念列表
     */
    @RequiresPermissions("personnel:armyIdea:export")
    @Log(title = "治军理念", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(IdwPeopleArmyIdea idwPeopleArmyIdea) {
        List<IdwPeopleArmyIdea> list = idwPeopleArmyIdeaService.selectIdwPeopleArmyIdeaList(idwPeopleArmyIdea);
        ExcelUtil<IdwPeopleArmyIdea> util = new ExcelUtil<IdwPeopleArmyIdea>(IdwPeopleArmyIdea.class);
        return util.exportExcel(list, "治军理念");
    }
}
