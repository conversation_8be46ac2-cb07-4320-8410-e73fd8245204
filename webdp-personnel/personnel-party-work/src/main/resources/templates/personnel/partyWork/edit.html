<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改人大、政协任职经历')" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-partyWork-edit" th:object="${idwPeoplePartyWork}">
            <input name="experienceId" th:field="*{experienceId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">人物编码：</label>
                <div class="col-sm-8">
                    <input name="peopleCode" th:field="*{peopleCode}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">类型：人大、政协：</label>
                <div class="col-sm-8">
                    <select name="type" class="form-control m-b" required>
                        <option value="">所有</option>
                    </select>
                    <span class="help-block m-b-none"><i class="fa fa-info-circle"></i> 代码生成请选择字典属性</span>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开始日期：</label>
                <div class="col-sm-8">
                    <input name="startDate" th:field="*{startDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结束日期：</label>
                <div class="col-sm-8">
                    <input name="endDate" th:field="*{endDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">届数：</label>
                <div class="col-sm-8">
                    <input name="sessionNum" th:field="*{sessionNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <input name="levelNation" th:field="*{levelNation}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <input name="levelProvince" th:field="*{levelProvince}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <input name="levelCity" th:field="*{levelCity}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">级别：</label>
                <div class="col-sm-8">
                    <input name="levelCounty" th:field="*{levelCounty}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">政协任职/人大任职：</label>
                <div class="col-sm-8">
                    <input name="post" th:field="*{post}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">界别：</label>
                <div class="col-sm-8">
                    <input name="circle" th:field="*{circle}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">组别：</label>
                <div class="col-sm-8">
                    <input name="group" th:field="*{group}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">专委会：</label>
                <div class="col-sm-8">
                    <input name="committee" th:field="*{committee}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">目前状态：</label>
                <div class="col-sm-8">
                    <input name="state" th:field="*{state}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">工作描述：</label>
                <div class="col-sm-8">
                    <textarea name="description" class="form-control">[[*{description}]]</textarea>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">数据来源：</label>
                <div class="col-sm-8">
                    <input name="source" th:field="*{source}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">删除标志：</label>
                <div class="col-sm-8">
                    <input name="isDelete" th:field="*{isDelete}" class="form-control" type="text" required>
                </div>
            </div>
        </form>
    </div>
    <th:block th:include="include :: footer" />
    <script th:inline="javascript">
        let prefix = ctx + "people/partyWork";
        $("#form-partyWork-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.save(prefix + "/edit", $('#form-partyWork-edit').serialize());
            }
        }
    </script>
</body>
</html>