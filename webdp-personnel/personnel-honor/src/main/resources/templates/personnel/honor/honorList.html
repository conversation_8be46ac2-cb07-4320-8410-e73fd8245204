<div class="container-div">
    <input type="hidden" value="人员荣誉奖项列表">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="form-honor">
                <input type="hidden" name="peopleCode" th:value="${peopleCode}">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>荣誉名称：</label>
                            <input type="text" name="title"/>
                        </li>
                        <li>
                            <label>颁发机构：</label>
                            <input type="text" name="issuer"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-honor', 'bootstrap-table-honor')"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-honor', 'bootstrap-table-honor')"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar-honor" role="group">
            <a class="btn btn-success" onclick="$.operate.add(null, null, 565)" shiro:hasPermission="people:honor:add">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit(null, null, 565)" shiro:hasPermission="people:honor:edit">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="people:honor:remove">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-bordered">
            <table id="bootstrap-table-honor" data-resizable="true"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var editFlag = [[${@permission.hasPermi('people:honor:edit')}]];
    var removeFlag = [[${@permission.hasPermi('people:honor:remove')}]];
    var prefix = ctx + "people/honor";

    $(function () {
        var options = {
            id: "bootstrap-table-honor",//指定表格ID
            toolbar: "toolbar-honor",//指定工具栏ID
            formId: "form-honor",
            showSearch: false,//隐藏表格搜索框功能
            url: prefix + "/honorList",
            createUrl: prefix + "/addHonor/" + peopleCode,
            updateUrl: prefix + "/editHonor/{id}",
            removeUrl: prefix + "/removeHonor",
            exportUrl: prefix + "/exportHonor",
            importUrl: prefix + "/importHonorData",
            importTemplateUrl: prefix + "/importHonorTemplate",
            modalName: "人员荣誉奖项",
            uniqueId: "honorId",
            columns: [{
                checkbox: true
            },
                {
                    align: 'center',
                    title: "序号",
                    width: 60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'title',
                    title: '荣誉名称'
                },
                {
                    align: 'center',
                    field: 'issueDate',
                    title: '获得时间',
                    width: 120
                },
                {
                    field: 'issuer',
                    title: '颁发机构'
                },
                {
                    align: 'center',
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            var sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0) {
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);//根据正则取出网址域名
                                    if (domainName != null && domainName != '') {
                                        if (i > 0) {
                                            html += '</br>'
                                        }
                                        html += "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != '') {
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    width: 140,
                    formatter: function (value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.honorId + '\', null, 565)"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.honorId + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>