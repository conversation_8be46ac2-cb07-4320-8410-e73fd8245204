
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="form-training">
                    <input type="hidden" name="peopleCode" th:value="${peopleCode}">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>开始日期：</label>
                                <input type="text" name="startDate"/>
                            </li>
                            <li>
                                <label>结束日期：</label>
                                <input type="text" name="endDate"/>
                            </li>
                            <li>
                                <label>培养机构：</label>
                                <input type="text" name="trainingInstitution"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search('form-training', 'bootstrap-table-training')"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset('form-training', 'bootstrap-table-training')"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar-training" role="group">
                <a class="btn btn-success" onclick="$.operate.add()" shiro:hasPermission="people:training:add">
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.edit()" shiro:hasPermission="people:training:edit">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" shiro:hasPermission="people:training:remove">
                    <i class="fa fa-remove"></i> 删除
                </a>
               <!-- <a class="btn btn-warning" onclick="$.table.exportExcel()" shiro:hasPermission="people:training:export">
                    <i class="fa fa-download"></i> 导出
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table-training"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        let editFlag = [[${@permission.hasPermi('people:training:edit')}]];
        let removeFlag = [[${@permission.hasPermi('people:training:remove')}]];
        let prefix = ctx + "people/training";

        $(function() {
            let options = {
                id: "bootstrap-table-training",          // 指定表格ID
                toolbar: "toolbar-training",   // 指定工具栏ID
                formId: "form-training",
                url: prefix + "/list",
                createUrl: prefix + "/add/"+peopleCode,
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "人员培养情况",
                uniqueId: "trainingId",
                columns: [{
                    checkbox: true
                },
                {
                    align: 'center',
                    title: "序号",
                    width: 60,
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'startDate',
                    title: '开始日期'
                },
                {
                    field: 'endDate',
                    title: '结束日期'
                },
                {
                    field: 'trainingInstitution',
                    title: '培养机构'
                },
                {
                    field: 'source',
                    title: '数据来源',
                    formatter: function (value, row, index) {
                        if (value != '' && value != null) {
                            let html = "";
                            var sourceArr = value.split(';');
                            if (sourceArr != null && sourceArr.length > 0){
                                let urlReg = /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+\.?/;
                                for (let i = 0; i < sourceArr.length; i++) {
                                    let source = sourceArr[i];
                                    let domainName = urlReg.exec(source);//根据正则取出网址域名
                                    if (domainName != null && domainName != '') {
                                        if (i > 0){
                                            html+= '</br>'
                                        }
                                        html+= "<a target='_blank' href='" + source + "' title='" + source + "'>" + (domainName[0] != null && domainName[0] != '' ? domainName[0] : source) + "</a>";
                                    }
                                }
                                if (html != null && html != ''){
                                    return html;
                                }
                            }
                        }
                        return $.table.tooltip(value, 8);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        let actions = [];
                        actions.push('<a class="btn btn-success btn-xs ' + editFlag + '" href="javascript:void(0)" onclick="$.operate.edit(\'' + row.trainingId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs ' + removeFlag + '" href="javascript:void(0)" onclick="$.operate.remove(\'' + row.trainingId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>