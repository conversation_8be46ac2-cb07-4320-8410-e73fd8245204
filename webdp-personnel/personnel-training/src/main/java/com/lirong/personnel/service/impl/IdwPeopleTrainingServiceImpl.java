package com.lirong.personnel.service.impl;

import java.util.*;

import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.personnel.mapper.IdwPeopleTrainingMapper;
import com.lirong.personnel.domain.IdwPeopleTraining;
import com.lirong.personnel.service.IdwPeopleTrainingService;
import com.lirong.common.core.text.Convert;

/**
 * 培养情况Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-06-07
 */
@Service
public class IdwPeopleTrainingServiceImpl implements IdwPeopleTrainingService {
    @Autowired
    private IdwPeopleTrainingMapper idwPeopleTrainingMapper;

    @Autowired//人员通用
    private IdwPeopleMainMapper idwPeopleMainMapper;

    /**
     * 查询培养情况
     *
     * @param trainingId 培养情况ID
     * @return 人员培养情况
     */
    @Override
    public IdwPeopleTraining selectIdwPeopleTrainingById(Long trainingId) {
        IdwPeopleTraining idwPeopleTraining = idwPeopleTrainingMapper.selectIdwPeopleTrainingById(trainingId);
        String startDate = idwPeopleTraining.getStartDate();
        if (StringUtils.isNotEmpty(startDate)) {
            Map<String, String> dateMap = DateUtils.splitDate(startDate);
            idwPeopleTraining.setStartYear(dateMap.get("year"));
            idwPeopleTraining.setStartMonth(dateMap.get("month"));
            idwPeopleTraining.setStartDay(dateMap.get("day"));
        }
        String endDate = idwPeopleTraining.getEndDate();
        if (StringUtils.isNotEmpty(endDate)) {
            Map<String, String> dateMap = DateUtils.splitDate(endDate);
            idwPeopleTraining.setEndYear(dateMap.get("year"));
            idwPeopleTraining.setEndMonth(dateMap.get("month"));
            idwPeopleTraining.setEndDay(dateMap.get("day"));
        }
        return idwPeopleTraining;
    }

    /**
     * 查询培养情况列表
     *
     * @param idwPeopleTraining 人员培养情况
     * @return 人员培养情况
     */
    @Override
    public List<IdwPeopleTraining> selectIdwPeopleTrainingList(IdwPeopleTraining idwPeopleTraining) {
        return idwPeopleTrainingMapper.selectIdwPeopleTrainingList(idwPeopleTraining);
    }

    /**
     * 新增培养情况
     *
     * @param idwPeopleTraining 人员培养情况
     * @return 结果
     */
    @Override
    public int insertIdwPeopleTraining(IdwPeopleTraining idwPeopleTraining) {
        idwPeopleTraining.setStartDate(DateUtils.mergeDate(idwPeopleTraining.getStartYear(), idwPeopleTraining.getStartMonth(), idwPeopleTraining.getStartDay()));
        idwPeopleTraining.setEndDate(DateUtils.mergeDate(idwPeopleTraining.getEndYear(), idwPeopleTraining.getEndMonth(), idwPeopleTraining.getEndDay()));
        idwPeopleTraining.setCreateBy(ShiroUtils.getUserName());
        idwPeopleTraining.setCreateTime(DateUtils.getNowDate());
        return idwPeopleTrainingMapper.insertIdwPeopleTraining(idwPeopleTraining);
    }

    /**
     * 修改培养情况
     *
     * @param idwPeopleTraining 人员培养情况
     * @return 结果
     */
    @Override
    public int updateIdwPeopleTraining(IdwPeopleTraining idwPeopleTraining) {
        idwPeopleTraining.setStartDate(DateUtils.mergeDate(idwPeopleTraining.getStartYear(), idwPeopleTraining.getStartMonth(), idwPeopleTraining.getStartDay()));
        idwPeopleTraining.setEndDate(DateUtils.mergeDate(idwPeopleTraining.getEndYear(), idwPeopleTraining.getEndMonth(), idwPeopleTraining.getEndDay()));
        idwPeopleTraining.setUpdateBy(ShiroUtils.getUserName());
        idwPeopleTraining.setUpdateTime(DateUtils.getNowDate());
        return idwPeopleTrainingMapper.updateIdwPeopleTraining(idwPeopleTraining);
    }

    /**
     * 删除培养情况对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwPeopleTrainingByIds(String ids) {
        String userName = ShiroUtils.getUserName();
        return idwPeopleTrainingMapper.deleteIdwPeopleTrainingByIds(Convert.toStrArray(ids), userName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param trainingList 培养情况集合
     * @return 结果
     */
    @Override
    public List<String> verifyImportTraining(List<IdwPeopleTraining> trainingList) {
        if (StringUtils.isNull(trainingList) || trainingList.size() < 1) {
            return null;
        }
        //处理完成人员培养情况数据列表
        List<IdwPeopleTraining> treatingAfterPeopleTrainingList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> peopleCodeList = (List<String>) CacheUtils.get("peopleImportTreatingAfterPeopleCodeList-" + userName);
        for (IdwPeopleTraining peopleTraining : trainingList) {
            row++;
            if (StringUtils.isNotNull(peopleTraining)) {
                //如果数据来源为空 赋值
                if (StringUtils.isBlank(peopleTraining.getSource())) {
                    peopleTraining.setSource("内部");
                }
                if (StringUtils.isBlank(peopleTraining.getPeopleCode())) {
                    isFailure = true;
                    msgList.add("培养情况,第" + row + "行," + " 人员编码为空");
                } else {
                    String peopleCode = peopleTraining.getPeopleCode();
                    IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(peopleCode);
                    if (StringUtils.isNull(people) && (StringUtils.isNull(peopleCodeList) || !peopleCodeList.contains(peopleCode))) {
                        isFailure = true;
                        msgList.add("培养情况,第" + row + "行," + " 人员编码（" + peopleCode + "）不存在");
                    }
                }
                //处理开始/结束日期
                String oldStartDate = peopleTraining.getStartDate();
                if (StringUtils.isNotBlank(oldStartDate)) {
                    String startDate = DateUtils.updateDateSeparator(oldStartDate, "-");
                    if (!DateUtils.isDate(startDate)) {
                        isFailure = true;
                        msgList.add("培养情况,第" + row + "行," + " 开始日期（" + oldStartDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    //赋值
                    peopleTraining.setStartDate(startDate);
                }
                String oldEndDate = peopleTraining.getEndDate();
                if (StringUtils.isNotBlank(oldEndDate)) {
                    String endDate = DateUtils.updateDateSeparator(oldEndDate, "-");
                    if (!DateUtils.isDate(endDate)) {
                        isFailure = true;
                        msgList.add("培养情况,第" + row + "行," + " 结束日期（" + oldEndDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    //赋值
                    peopleTraining.setEndDate(endDate);
                }
                treatingAfterPeopleTrainingList.add(peopleTraining);
            }
        }
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("peopleImportTreatingAfterPeopleTrainingList-" + userName, treatingAfterPeopleTrainingList);
        }
        return null;
    }

    /**
     * 导入培养情况信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @return 结果
     */
    @Override
    public String importTraining(boolean updateSupport, String operName) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwPeopleTraining> trainingList = (List<IdwPeopleTraining>) CacheUtils.get("peopleImportTreatingAfterPeopleTrainingList-" + operName);
        if (StringUtils.isNull(trainingList) || trainingList.size() < 1) {
            return null;
        }
        for (IdwPeopleTraining peopleTraining : trainingList) {
            if (StringUtils.isNotNull(peopleTraining)) {
                // 判断是否存在
                IdwPeopleTraining trainingMain = idwPeopleTrainingMapper.selectByPeopleCodeAndStartDateAndTrainingInstitution(peopleTraining.getPeopleCode(), peopleTraining.getStartDate(), peopleTraining.getTrainingInstitution());
                if (StringUtils.isNull(trainingMain)) {
                    // 新增
                    insertCount++;
                    peopleTraining.setCreateBy(operName);
                    peopleTraining.setCreateTime(nowDate);
                    idwPeopleTrainingMapper.insertIdwPeopleTraining(peopleTraining);
                } else if (updateSupport) {
                    //更新
                    updateCount++;
                    peopleTraining.setTrainingId(trainingMain.getTrainingId());
                    peopleTraining.setUpdateBy(operName);
                    peopleTraining.setUpdateTime(nowDate);
                    idwPeopleTrainingMapper.updateIdwPeopleTraining(peopleTraining);
                }
            }
        }
        CacheUtils.remove("peopleImportTreatingAfterPeopleTrainingList-" + operName);
        return "教育经历共：" + trainingList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 统计提示信息
     *
     * @return 模块名称
     */
    @Override
    public String getStatisticalTips() {
        return "培养情况";
    }

    /**
     * 根据人员编码查询数据量
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public Integer getStatisticalQuantity(String peopleCode) {
        return idwPeopleTrainingMapper.selectCountByPeopleCode(peopleCode);
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if (type.equals("people")) {
            return idwPeopleTrainingMapper.deleteByPeopleCodes(codes, loginName, deleteTime);
        }
        return 1;
    }
}
