package com.lirong.personnel.news.service.impl;

import java.io.File;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.lirong.common.config.WebdpConfig;
import com.lirong.common.constant.Constants;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.DateUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.common.mapper.IdwPeopleMainMapper;
import com.lirong.personnel.news.mapper.IdwPeopleNewsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.lirong.personnel.news.domain.IdwPeopleNews;
import com.lirong.personnel.news.service.IdwPeopleNewsService;
import com.lirong.common.core.text.Convert;

/**
 * 人员新闻报道Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-01-07
 */
@Service
public class IdwPeopleNewsServiceImpl implements IdwPeopleNewsService {
    @Autowired
    private IdwPeopleNewsMapper idwPeopleNewsMapper;
    @Autowired//人员通用
    private IdwPeopleMainMapper idwPeopleMainMapper;

    /**
     * 查询人员新闻报道
     *
     * @param newsId 人员新闻报道ID
     * @return 人员新闻报道
     */
    @Override
    public IdwPeopleNews selectIdwPeopleNewsById(Long newsId) {
        IdwPeopleNews news = idwPeopleNewsMapper.selectIdwPeopleNewsById(newsId);
        if (StringUtils.isNotBlank(news.getPublishDate())) {
            Map<String, String> dateMap = DateUtils.splitDate(news.getPublishDate());
            news.setYear(dateMap.get("year"));
            news.setMonth(dateMap.get("month"));
            news.setDay(dateMap.get("day"));
        }
        return news;
    }

    /**
     * 查询人员新闻报道列表
     *
     * @param idwPeopleNews 人员新闻报道
     * @return 人员新闻报道
     */
    @Override
    public List<IdwPeopleNews> selectIdwPeopleNewsList(IdwPeopleNews idwPeopleNews) {
        return idwPeopleNewsMapper.selectIdwPeopleNewsList(idwPeopleNews);
    }

    /**
     * 新增人员新闻报道
     *
     * @param idwPeopleNews 人员新闻报道
     * @return 结果
     */
    @Override
    public int insertIdwPeopleNews(IdwPeopleNews idwPeopleNews) {
        if (StringUtils.isNotEmpty(idwPeopleNews.getYear())) {
            idwPeopleNews.setPublishDate(idwPeopleNews.getYear());
            if (StringUtils.isNotEmpty(idwPeopleNews.getMonth())) {
                idwPeopleNews.setPublishDate(idwPeopleNews.getYear() + "-" + idwPeopleNews.getMonth());
                if (StringUtils.isNotEmpty(idwPeopleNews.getDay())) {
                    idwPeopleNews.setPublishDate(idwPeopleNews.getYear() + "-" + idwPeopleNews.getMonth() + "-" + idwPeopleNews.getDay());
                }
            }
        }
        idwPeopleNews.setCreateBy(ShiroUtils.getUserName());
        idwPeopleNews.setCreateTime(DateUtils.getNowDate());
        return idwPeopleNewsMapper.insertIdwPeopleNews(idwPeopleNews);
    }

    /**
     * 修改人员新闻报道
     *
     * @param idwPeopleNews 人员新闻报道
     * @return 结果
     */
    @Override
    public int updateIdwPeopleNews(IdwPeopleNews idwPeopleNews) {
        if (StringUtils.isNotEmpty(idwPeopleNews.getYear())) {
            idwPeopleNews.setPublishDate(idwPeopleNews.getYear());
            if (StringUtils.isNotEmpty(idwPeopleNews.getMonth())) {
                idwPeopleNews.setPublishDate(idwPeopleNews.getYear() + "-" + idwPeopleNews.getMonth());
                if (StringUtils.isNotEmpty(idwPeopleNews.getDay())) {
                    idwPeopleNews.setPublishDate(idwPeopleNews.getYear() + "-" + idwPeopleNews.getMonth() + "-" + idwPeopleNews.getDay());
                }
            }
        }
        idwPeopleNews.setUpdateBy(ShiroUtils.getUserName());
        idwPeopleNews.setUpdateTime(DateUtils.getNowDate());
        return idwPeopleNewsMapper.updateIdwPeopleNews(idwPeopleNews);
    }

    /**
     * 删除人员新闻报道对象
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    @Override
    public int deleteIdwPeopleNewsByIds(String ids) {
        String loginName = ShiroUtils.getUserName();
        return idwPeopleNewsMapper.deleteIdwPeopleNewsByIds(Convert.toStrArray(ids), loginName);
    }

    /**
     * 校验Excel导入数据
     *
     * @param newsList         新闻报道集合
     * @param type             类型 (新闻报道 news/统战活动 activity)
     * @param filePathIndexMap key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
     * @param filePathList     上传后的文件路径
     * @param baseDir          临时文件夹目录
     * @return 结果
     */
    @Override
    public List<String> verifyImportNews(List<IdwPeopleNews> newsList, String type, Map<String, Integer> filePathIndexMap, List<String> filePathList, String baseDir, boolean updateSupport) {
        if (StringUtils.isNull(newsList) || newsList.size() < 1) {
            return null;
        }
        //处理完成数据列表
        List<IdwPeopleNews> treatingAfterPeopleNewsList = new ArrayList<>();
        int row = 1;
        boolean isFailure = false;
        List<String> msgList = new ArrayList<>();
        String userName = ShiroUtils.getUserName();
        List<String> peopleCodeList = (List<String>) CacheUtils.get("peopleImportTreatingAfterPeopleCodeList-" + userName);
        List<String> deleteFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingDeleteFilePathList-" + userName);//需要删除的文件路径
        if (StringUtils.isNull(deleteFilePathList)) {
            deleteFilePathList = new ArrayList<>();
        }
        List<String> temporaryFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingTemporaryFilePathList-" + userName);//需要拷贝的文件路径
        if (StringUtils.isNull(temporaryFilePathList)) {
            temporaryFilePathList = new ArrayList<>();
        }
        List<String> newFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingNewFilePathList-" + userName);//拷贝文件目标路径
        if (StringUtils.isNull(newFilePathList)) {
            newFilePathList = new ArrayList<>();
        }
        String title = type.equals("news") ? "新闻报道" : "统战活动";
        for (IdwPeopleNews news : newsList) {
            row++;
            if (StringUtils.isNotNull(news)) {
                news.setType(type);//赋值类型
                if (StringUtils.isBlank(news.getPeopleCode())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 人员编码为空");
                } else {
                    String peopleCode = news.getPeopleCode();
                    //IdwPeopleMain people = idwPeopleMainMapper.selectPeopleByPeopleCode(peopleCode);
                    List<IdwPeopleMain> people = idwPeopleMainMapper.selectByPeopleName(null, news.getPeopleCode());
                    if (StringUtils.isNotNull(people) && people.size() == 1) {
                        news.setPeopleCode(people.get(0).getPeopleCode());
                    } else {
                        isFailure = true;
                        msgList.add(title + ",第" + row + "行," + " 人员编码（" + peopleCode + "）不存在");
                    }
                }
                if (StringUtils.isBlank(news.getTitle())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 标题为空");
                }
                if (StringUtils.isBlank(news.getContent())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 内容为空");
                }
                if (StringUtils.isBlank(news.getUrl())) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 链接地址为空");
                }
                if (StringUtils.isBlank(news.getTags())) {
                    news.setTags(news.getTags().replaceAll("、", ","));
                }
                String oldPublishDate = news.getPublishDate();
                if (StringUtils.isBlank(oldPublishDate)) {
                    isFailure = true;
                    msgList.add(title + ",第" + row + "行," + " 发布日期为空");
                } else {
                    String publishDate = DateUtils.updateDateSeparator(oldPublishDate);
                    if (!DateUtils.isDate(publishDate)) {
                        isFailure = true;
                        msgList.add(title + ",第" + row + "行," + " 发布日期（" + oldPublishDate + "）格式错误，格式为：" + DateUtils.YYYY_MM_DD);
                    }
                    news.setPublishDate(publishDate);
                }
                // 判断是否存在
                IdwPeopleNews oldNews = idwPeopleNewsMapper.selectByPeopleCodeAndTitleAndDate(news.getType(), news.getPeopleCode(), news.getTitle(), news.getPublishDate());
                if (StringUtils.isNotNull(oldNews)) {
                    news.setNewsId(oldNews.getNewsId());
                }

                //校验图片
                String oldThumbnail = news.getThumbnail();
                if (StringUtils.isNotBlank(oldThumbnail)) {
                    if (oldThumbnail.contains(Constants.RESOURCE_PREFIX)) {
                        if (!new File(oldThumbnail).exists()) {
                            isFailure = true;
                            msgList.add(title + ",第" + row + "行," + " 缩略图（" + oldThumbnail + "）不存在");
                        }
                    } else {
                        //替换文件名称中的特殊字符
                        oldThumbnail = FileUploadUtils.replaceFileNameSpecialCharacter(oldThumbnail).toLowerCase();
                        if (StringUtils.isNotNull(filePathIndexMap) && filePathIndexMap.size() > 0) {
                            Integer index = filePathIndexMap.get(oldThumbnail);
                            //不存在 匹配文件名称
                            if (StringUtils.isNull(index)) {
                                String excelFileName = StringUtils.getFileName(oldThumbnail);
                                //使用文件名称匹配
                                for (String key : filePathIndexMap.keySet()) {
                                    String fileName = StringUtils.getFileName(key);
                                    if (excelFileName.equals(fileName)) {
                                        index = filePathIndexMap.get(key);
                                        break;
                                    }
                                }
                            }
                            if (StringUtils.isNotNull(index)) {
                                //当数据存在但是支持更新或数据不存在时处理图片
                                if ((StringUtils.isNotNull(oldNews) && updateSupport) || StringUtils.isNull(oldNews)) {
                                    //保存需要删除的图片路径
                                    if (StringUtils.isNotNull(oldNews) && StringUtils.isNotBlank(oldNews.getThumbnail())) {
                                        deleteFilePathList.add(oldNews.getThumbnail());
                                    }

                                    //获取文件名称
                                    String temporaryFilePath = filePathList.get(index);
                                    String[] filePathArr = temporaryFilePath.split("/");
                                    //根据文件名称与纬度路径生成新的文件路径
                                    String newFilePath = FileUploadUtils.generateFilePath(WebdpConfig.getPersonnelPath(), filePathArr[filePathArr.length - 1]);
                                    //保存文件路径
                                    temporaryFilePathList.add(temporaryFilePath);
                                    //保存新的文件路径
                                    newFilePathList.add(newFilePath);
                                    //赋值新图片路径
                                    news.setThumbnail(newFilePath);
                                }
                            } else {
                                isFailure = true;
                                msgList.add(title + ",第" + row + "行," + " 缩略图（" + oldThumbnail + "）不存在");
                            }
                        } else {
                            isFailure = true;
                            msgList.add(title + ",第" + row + "行," + " 缩略图不为空且压缩包文件为空");
                        }
                    }
                }
                treatingAfterPeopleNewsList.add(news);
            }
        }
        isFailure = false;
        if (isFailure) {
            return msgList;
        } else {
            CacheUtils.put("peopleImportTreatingDeleteFilePathList-" + userName, deleteFilePathList);
            CacheUtils.put("peopleImportTreatingTemporaryFilePathList-" + userName, temporaryFilePathList);
            CacheUtils.put("peopleImportTreatingNewFilePathList-" + userName, newFilePathList);
            CacheUtils.put("peopleImportTreatingAfterPeopleNewsList-" + type + userName, treatingAfterPeopleNewsList);
        }
        return null;
    }

    /**
     * 导入新闻报道信息
     *
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @param operName      操作用户
     * @param type          类型 (新闻报道 news/统战活动 activity)
     * @return 结果
     */
    @Override
    public String importNews(boolean updateSupport, String operName, String type) {
        Date nowDate = DateUtils.getNowDate();
        long insertCount = 0;
        long updateCount = 0;
        List<IdwPeopleNews> newsList = (List<IdwPeopleNews>) CacheUtils.get("peopleImportTreatingAfterPeopleNewsList-" + type + operName);
        if (StringUtils.isNull(newsList) || newsList.size() < 1) {
            return null;
        }
        String title = "";
        for (IdwPeopleNews news : newsList) {
            if (StringUtils.isNotNull(news)) {
                if (StringUtils.isBlank(title)) {
                    title = news.getType().equals("news") ? "新闻报道" : "统战活动";
                }
                if (StringUtils.isNull(news.getNewsId())) {
                    // 新增
                    insertCount++;
                    news.setCreateBy(operName);
                    news.setCollectionTime(nowDate);
                    idwPeopleNewsMapper.insertIdwPeopleNews(news);
                } else if (updateSupport) {
                    // 更新
                    updateCount++;
                    news.setUpdateBy(operName);
                    news.setUpdateTime(nowDate);
                    idwPeopleNewsMapper.updateIdwPeopleNews(news);
                }
            }
        }
        CacheUtils.remove("peopleImportTreatingAfterPeopleNewsList-" + type + operName);
        return title + "共：" + newsList.size() + "条" + ",新增：" + insertCount + "条" + ",修改：" + updateCount + "条";
    }

    /**
     * 根据人员编码查询
     *
     * @param peopleCodes 人员编码
     * @return 结果
     */
    @Override
    public List<IdwPeopleNews> selectByPeopleCodes(String[] peopleCodes) {
        return idwPeopleNewsMapper.selectByPeopleCodes(peopleCodes);
    }

    /**
     * 统计提示信息
     *
     * @return 模块名称
     */
    @Override
    public String getStatisticalTips() {
        return "新闻报道";
    }

    /**
     * 根据人员编码查询人员新闻报道数据量
     *
     * @param peopleCode 人员编码
     * @return 结果
     */
    @Override
    public Integer getStatisticalQuantity(String peopleCode) {
        return idwPeopleNewsMapper.selectIdwPeopleNewsDataCountByPeopleCode(peopleCode);
    }

    /**
     * 根据编码删除
     *
     * @param type       类型
     * @param codes      编码
     * @param loginName  当年登录用户
     * @param deleteTime 删除时间
     * @return 结果
     */
    @Override
    public int deleteByCode(String type, String[] codes, String loginName, String deleteTime) {
        if (type.equals("people")) {
            return idwPeopleNewsMapper.deletePeopleNewsByPeopleCodes(codes, loginName, deleteTime);
        }
        return 1;
    }

    /**
     * 根据文件名称修改文件路径
     *
     * @param type     业务类型 organization/personnel/weaponry
     * @param fileName 文件名称
     * @param fileUrl  文件路径
     * @param userName 当前登录用户
     * @return 结果
     */
    @Override
    public int accordingFileNameModificationFileUrl(String type, String fileName, String fileUrl, String userName) {
        if (type.equals("personnel")) {
            int count = idwPeopleNewsMapper.selectCountByFileName(fileName);
            if (count > 0) {
                idwPeopleNewsMapper.updateAvatarByFileName(fileName, fileUrl, userName);
                return 1;
            }
        }
        return 0;
    }

    /**
     * 获取文件路径
     *
     * @param type 业务类型
     * @return 文件路径
     */
    @Override
    public List<String> getFilePath(List<String> type) {
        if (StringUtils.isNotNull(type) && type.contains("personnel")) {
            return idwPeopleNewsMapper.selectAllFilePath();
        } else {
            return null;
        }
    }
}
