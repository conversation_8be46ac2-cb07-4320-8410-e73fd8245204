package com.lirong.personnel.education.domain;

import com.lirong.common.annotation.Excel;
import com.lirong.common.core.domain.BaseEntity;
import com.lirong.common.utils.StringUtils;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 人员教育经历对象 idw_people_education
 *
 * <AUTHOR>
 * @date 2021-01-04
 */
public class IdwPeopleEducation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 教育经历ID
     */
    private Long educationId;

    /**
     * 人员编码
     */
    @Excel(name = "人员编码")
    private String peopleCode;

    /**
     * 院校名称
     */
    @Excel(name = "院校名称")
    private String college;

    /**
     * 开始日期
     */
    @Excel(name = "开始日期")
    private String startDate;

    /**
     * 结束日期
     */
    @Excel(name = "结束日期")
    private String endDate;

    /**
     * 所学专业
     */
    @Excel(name = "专业|所学专业")
    private String major;

    /**
     * 学位学历
     */
    @Excel(name = "学位")
    private String degree;

    /**
     * 数据来源
     */
    @Excel(name = "数据来源")
    private String source;

    /**
     * 是否删除，0-未删除，1-已删除
     */
    //@Excel(name = "是否删除，0-未删除，1-已删除")
    private Integer isDelete;

    private String startYear;

    private String startMonth;

    private String endYear;

    private String endMonth;

    public void setEducationId(Long educationId) {
        this.educationId = educationId;
    }

    public Long getEducationId() {
        return educationId;
    }

    public void setPeopleCode(String peopleCode) {
        this.peopleCode = peopleCode;
    }

    public String getPeopleCode() {
        return peopleCode;
    }

    public void setCollege(String college) {
        this.college = college;
    }

    public String getCollege() {
        return college;
    }

    public void setStartDate(String startDate) {
        this.startDate = startDate;
    }

    public String getStartDate() {
        return startDate;
    }

    public void setEndDate(String endDate) {
        this.endDate = endDate;
    }

    public String getEndDate() {
        return endDate;
    }

    public void setMajor(String major) {
        this.major = major;
    }

    public String getMajor() {
        return major;
    }

    public void setDegree(String degree) {
        this.degree = degree;
    }

    public String getDegree() {
        return degree;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getSource() {
        return StringUtils.isNotBlank(source) ? source.replaceAll("；", ";").replaceAll("(\r\n|\r|\n|\n\r)", ";") : source;
    }

    public void setIsDelete(Integer isDelete) {
        this.isDelete = isDelete;
    }

    public Integer getIsDelete() {
        return isDelete;
    }

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getStartYear() {
        return startYear;
    }

    public void setStartYear(String startYear) {
        this.startYear = startYear;
    }

    public String getStartMonth() {
        return startMonth;
    }

    public void setStartMonth(String startMonth) {
        this.startMonth = startMonth;
    }

    public String getEndYear() {
        return endYear;
    }

    public void setEndYear(String endYear) {
        this.endYear = endYear;
    }

    public String getEndMonth() {
        return endMonth;
    }

    public void setEndMonth(String endMonth) {
        this.endMonth = endMonth;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("educationId", getEducationId())
                .append("peopleCode", getPeopleCode())
                .append("college", getCollege())
                .append("startDate", getStartDate())
                .append("endDate", getEndDate())
                .append("major", getMajor())
                .append("degree", getDegree())
                .append("startYear", getStartYear())
                .append("startMonth", getStartMonth())
                .append("endYear", getEndYear())
                .append("endMonth", getEndMonth())
                .append("source", getSource())
                .append("isDelete", getIsDelete())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .toString();
    }
}
