package com.lirong.personnel.officer.controller;

import com.lirong.common.annotation.Log;
import com.lirong.common.config.WebdpConfig;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.enums.BusinessType;
import com.lirong.common.utils.CacheUtils;
import com.lirong.common.utils.ShiroUtils;
import com.lirong.common.utils.StringUtils;
import com.lirong.common.utils.file.FileUploadUtils;
import com.lirong.common.utils.file.FileUtils;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.multimedia.domain.IdwMultimedia;
import com.lirong.multimedia.service.IdwMultimediaService;
import com.lirong.personnel.domain.IdwPeopleAchievement;
import com.lirong.personnel.education.domain.IdwPeopleEducation;
import com.lirong.personnel.education.service.IdwPeopleEducationService;
import com.lirong.personnel.honor.domain.IdwPeopleHonor;
import com.lirong.personnel.honor.service.IdwPeopleHonorService;
import com.lirong.personnel.idea.domain.IdwPeopleArmyIdea;
import com.lirong.personnel.idea.service.IdwPeopleArmyIdeaService;
import com.lirong.personnel.media.domain.IdwPeopleSocialAccount;
import com.lirong.personnel.media.domain.IdwPeopleSocialMedia;
import com.lirong.personnel.media.service.IdwPeopleSocialAccountService;
import com.lirong.personnel.media.service.IdwPeopleSocialMediaService;
import com.lirong.personnel.news.domain.IdwPeopleNews;
import com.lirong.personnel.news.service.IdwPeopleNewsService;
import com.lirong.personnel.officer.domain.IdwOfficerMain;
import com.lirong.personnel.common.domain.IdwPeopleMain;
import com.lirong.personnel.officer.service.IdwOfficerMainService;
import com.lirong.personnel.common.service.IdwPeopleMainService;
import com.lirong.personnel.relationship.domain.IdwPeopleRelationship;
import com.lirong.personnel.relationship.service.IdwPeopleRelationshipService;
import com.lirong.personnel.service.IdwPeopleAchievementService;
import com.lirong.personnel.work.domain.IdwPeopleWorkExperience;
import com.lirong.personnel.work.service.IdwPeopleWorkExperienceService;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 政府军队官员Controller
 *
 * <AUTHOR>
 * @date 2020-12-25
 */
@Controller
@RequestMapping("/people/officer")
public class IdwOfficerMainController extends BaseController {
    public static final Logger log = LoggerFactory.getLogger(IdwOfficerMainController.class);

    private String prefix = "personnel/officer";

    @Autowired//人员通用
    private IdwPeopleMainService idwPeopleMainService;
    @Autowired//新闻报道
    private IdwPeopleNewsService idwPeopleNewsService;
    @Autowired//多媒体
    private IdwMultimediaService idwMultimediaService;
    @Autowired//政府军队官员
    private IdwOfficerMainService idwOfficerMainService;
    @Autowired//荣誉奖项
    private IdwPeopleHonorService idwPeopleHonorService;
    @Autowired//教育经历
    private IdwPeopleEducationService idwPeopleEducationService;
    @Autowired//治军理念
    private IdwPeopleArmyIdeaService idwPeopleArmyIdeaService;
    @Autowired//社交媒体
    private IdwPeopleSocialMediaService idwPeopleSocialMediaService;
    @Autowired//人员关系
    private IdwPeopleRelationshipService idwPeopleRelationshipService;
    @Autowired//工作成果
    private IdwPeopleAchievementService idwPeopleAchievementService;
    @Autowired//社交媒体账号
    private IdwPeopleSocialAccountService idwPeopleSocialAccountService;
    @Autowired//工作经历
    private IdwPeopleWorkExperienceService idwPeopleWorkExperienceService;

    /**
     * 政府军队官员列表跳转页面
     *
     * @return 结果
     */
    @RequiresPermissions("people:officer:view")
    @GetMapping()
    public String people() {
        return prefix + "/officerPeopleList";
    }

    /**
     * 新增政府军队官员跳转页面
     */
    @GetMapping("/add")
    public String addOfficer() {
        return prefix + "/addOfficer";
    }

    /**
     * 修改政府军队官员跳转选项卡页面
     */
    @GetMapping("/edit/{peopleCode}")
    public String edit(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        mmap.put("peopleCode", peopleCode);
        return prefix + "/officerTabs";
    }

    /**
     * 修改政府军队官员跳转修改页面
     */
    @GetMapping("/peopleOfficerEdit/{peopleCode}")
    public String peopleOfficerEdit(@PathVariable("peopleCode") String peopleCode, ModelMap mmap) {
        IdwPeopleMain idwPeopleMain = idwPeopleMainService.selectPeopleByPeopleCode(peopleCode);
        mmap.put("idwPeopleMain", idwPeopleMain);
        return prefix + "/editOfficer";
    }

    /**
     * 人员相关文件上传保存
     */
    @RequiresPermissions("people:officer:edit")
    @PostMapping(value = {"/saveBatchUploadFile"})
    @ResponseBody
    public AjaxResult saveBatchUploadFile(String excelFileName, String excelFileUrl) {
        return AjaxResult.success(idwOfficerMainService.saveBatchUploadFile(excelFileName, excelFileUrl));
    }

    /**
     * 导入下载模板
     *
     * @return 结果
     */
    @RequiresPermissions("people:officer:import")
    @GetMapping("/importOfficerTemplate")
    @ResponseBody
    public AjaxResult importOfficerTemplate() {
        ExcelUtil<IdwOfficerMain> util = new ExcelUtil<IdwOfficerMain>(IdwOfficerMain.class);
        return util.importTemplateExcel("政府军队官员");
    }

    /**
     * 导入
     *
     * @param excelFile     数据列表
     * @param zipFile       相关文件
     * @param updateSupport 是否支持更新, 如果已存在, 则进行更新
     * @return 结果
     * @throws Exception
     */
    @Log(title = "政府军队官员", businessType = BusinessType.IMPORT)
    @RequiresPermissions("people:officer:import")
    @PostMapping("/importData")
    @ResponseBody
    public Map<String, Object> importData(MultipartFile excelFile, MultipartFile zipFile, boolean updateSupport) throws Exception {
        String operName = ShiroUtils.getUserName();
        Map<String, Object> msgMap = new HashMap<>();
        //解压压缩包
        //key 文件在压缩包中的相对路径 value 文件对应在filePathList中的索引
        Map<String, Integer> filePathIndexMap = new HashMap<>();
        //文件路径
        List<String> filePathList = new ArrayList<>();
        //临时文件夹路径
        String baseDir = null;
        if (StringUtils.isNotNull(zipFile)) {
            Map<String, Object> map = null;
            try {
                map = FileUploadUtils.decompressionZipImage(zipFile);
            } catch (IOException e) {
                e.printStackTrace();
            }
            filePathIndexMap = (Map<String, Integer>) map.get("filePathIndexMap");
            filePathList = (List<String>) map.get("filePath");
            baseDir = (String) map.get("baseDir");
        }
        //校验结果
        List<String> formatErrorMessage = new ArrayList<>();
        ExcelUtil<IdwOfficerMain> util = new ExcelUtil<IdwOfficerMain>(IdwOfficerMain.class);
        List<IdwOfficerMain> peopleList = util.importExcel("政府军队官员", excelFile.getInputStream());
        List<String> peopleMessage = idwOfficerMainService.verifyImportOfficer(peopleList, filePathIndexMap, filePathList, baseDir, updateSupport);
        if (StringUtils.isNotNull(peopleMessage)) {
            formatErrorMessage.addAll(peopleMessage);
        }
        ExcelUtil<IdwPeopleEducation> peopleEducationExcelUtil = new ExcelUtil<IdwPeopleEducation>(IdwPeopleEducation.class);
        List<IdwPeopleEducation> educationList = peopleEducationExcelUtil.importExcel("教育经历", excelFile.getInputStream());
        List<String> educationMessage = idwPeopleEducationService.verifyImportEducation(educationList);
        if (StringUtils.isNotNull(educationMessage)) {
            formatErrorMessage.addAll(educationMessage);
        }
        ExcelUtil<IdwPeopleSocialAccount> socialAccountUtil = new ExcelUtil<IdwPeopleSocialAccount>(IdwPeopleSocialAccount.class);
        List<IdwPeopleSocialAccount> socialAccountList = socialAccountUtil.importExcel("社交媒体账号", excelFile.getInputStream());
        List<String> socialAccountMessage = idwPeopleSocialAccountService.verifyImportSocialAccount(socialAccountList, filePathIndexMap, filePathList, baseDir, updateSupport);
        if (StringUtils.isNotNull(socialAccountMessage)) {
            formatErrorMessage.addAll(socialAccountMessage);
        }
        ExcelUtil<IdwPeopleSocialMedia> socialMediautil = new ExcelUtil<IdwPeopleSocialMedia>(IdwPeopleSocialMedia.class);
        List<IdwPeopleSocialMedia> socialMediaList = socialMediautil.importExcel("社交媒体", excelFile.getInputStream());
        List<String> socialMediaMessage = idwPeopleSocialMediaService.verifyImportSocialMedia(socialMediaList);
        if (StringUtils.isNotNull(socialMediaMessage)) {
            formatErrorMessage.addAll(socialMediaMessage);
        }
        ExcelUtil<IdwPeopleNews> peopleNewsExcelUtil = new ExcelUtil<IdwPeopleNews>(IdwPeopleNews.class);
        List<IdwPeopleNews> newsList = peopleNewsExcelUtil.importExcel("新闻报道", excelFile.getInputStream());
        List<String> newsMessage = idwPeopleNewsService.verifyImportNews(newsList, "news", filePathIndexMap, filePathList, baseDir, updateSupport);
        if (StringUtils.isNotNull(newsMessage)) {
            formatErrorMessage.addAll(newsMessage);
        }
        ExcelUtil<IdwPeopleWorkExperience> workExperienceExcelUtil = new ExcelUtil<IdwPeopleWorkExperience>(IdwPeopleWorkExperience.class);
        List<IdwPeopleWorkExperience> workExperienceList = workExperienceExcelUtil.importExcel("工作经历", excelFile.getInputStream());
        List<String> workExperienceMessage = idwPeopleWorkExperienceService.verifyImportWorkExperience(workExperienceList);
        if (StringUtils.isNotNull(workExperienceMessage)) {
            formatErrorMessage.addAll(workExperienceMessage);
        }
        ExcelUtil<IdwPeopleHonor> peopleHonorExcelUtil = new ExcelUtil<IdwPeopleHonor>(IdwPeopleHonor.class);
        List<IdwPeopleHonor> honorList = peopleHonorExcelUtil.importExcel("荣誉奖项", excelFile.getInputStream());
        List<String> honorMessage = idwPeopleHonorService.verifyImportHonor(honorList, "荣誉奖项");
        if (StringUtils.isNotNull(honorMessage)) {
            formatErrorMessage.addAll(honorMessage);
        }
        ExcelUtil<IdwPeopleRelationship> peopleRelationshipExcelUtil = new ExcelUtil<IdwPeopleRelationship>(IdwPeopleRelationship.class);
        List<IdwPeopleRelationship> relationshipList = peopleRelationshipExcelUtil.importExcel("人员关系", excelFile.getInputStream());
        List<String> relationshipMessage = idwPeopleRelationshipService.verifyImportRelationship(relationshipList, filePathIndexMap, filePathList, baseDir, updateSupport, "人员关系");
        if (StringUtils.isNotNull(relationshipMessage)) {
            formatErrorMessage.addAll(relationshipMessage);
        }
        ExcelUtil<IdwPeopleArmyIdea> peopleArmyIdeaExcelUtil = new ExcelUtil<IdwPeopleArmyIdea>(IdwPeopleArmyIdea.class);
        List<IdwPeopleArmyIdea> armyIdeaList = peopleArmyIdeaExcelUtil.importExcel("治军理念", excelFile.getInputStream());
        List<String> armyIdeaMessage = idwPeopleArmyIdeaService.verifyImportArmyIdea(armyIdeaList);
        if (StringUtils.isNotNull(armyIdeaMessage)) {
            formatErrorMessage.addAll(armyIdeaMessage);
        }
        ExcelUtil<IdwPeopleAchievement> peopleAchievementExcelUtil = new ExcelUtil<IdwPeopleAchievement>(IdwPeopleAchievement.class);
        List<IdwPeopleAchievement> projectList = peopleAchievementExcelUtil.importExcel("参与的项目", excelFile.getInputStream());
        List<String> projectMessage = idwPeopleAchievementService.verifyImportAchievement(projectList, filePathIndexMap, filePathList, baseDir, updateSupport, "参与的项目");
        if (StringUtils.isNotNull(projectMessage)) {
            formatErrorMessage.addAll(projectMessage);
        }
        ExcelUtil<IdwMultimedia> peopleMultimediaExcelUtil = new ExcelUtil<IdwMultimedia>(IdwMultimedia.class);
        List<IdwMultimedia> multimediaList = peopleMultimediaExcelUtil.importExcel("图片视频", excelFile.getInputStream());
        List<String> multimediaMessage = idwMultimediaService.verifyImportMultimedia(multimediaList, filePathIndexMap, filePathList, baseDir, updateSupport, "personnel", "图片视频");
        if (StringUtils.isNotNull(multimediaMessage)) {
            formatErrorMessage.addAll(multimediaMessage);
        }
        //清除人员编码缓存
        CacheUtils.remove("peopleImportTreatingAfterPeopleCodeList-" + operName);
        //不为空 出现格式错误
        if (formatErrorMessage.size() > 0) {
            //清除缓存
            CacheUtils.remove("peopleImportTreatingDeleteFilePathList-" + operName);
            CacheUtils.remove("peopleImportTreatingTemporaryFilePathList-" + operName);
            CacheUtils.remove("peopleImportTreatingNewFilePathList-" + operName);
            //删除临时文件夹
            if (StringUtils.isNotBlank(baseDir)) {
                FileUploadUtils.deleteDir(new File(baseDir));
            }
            msgMap.put("status", false);
            msgMap.put("msg", formatErrorMessage);
            return msgMap;
        }

        //处理文件
        List<String> deleteFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingDeleteFilePathList-" + operName);
        //需要删除的文件
        if (StringUtils.isNotNull(deleteFilePathList) && deleteFilePathList.size() > 0) {
            FileUploadUtils.batchDeleteFile(deleteFilePathList);
        }
        CacheUtils.remove("peopleImportTreatingDeleteFilePathList-" + operName);
        //需要拷贝的文件
        List<String> temporaryFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingTemporaryFilePathList-" + operName);
        List<String> newFilePathList = (List<String>) CacheUtils.get("peopleImportTreatingNewFilePathList-" + operName);
        if (StringUtils.isNotNull(temporaryFilePathList) && temporaryFilePathList.size() > 0) {
            FileUploadUtils.batchCopyFile(temporaryFilePathList, newFilePathList);
        }
        CacheUtils.remove("peopleImportTreatingTemporaryFilePathList-" + operName);
        CacheUtils.remove("peopleImportTreatingNewFilePathList-" + operName);
        //删除临时文件夹
        if (StringUtils.isNotBlank(baseDir)) {
            FileUploadUtils.deleteDir(new File(baseDir));
        }
        //提示信息
        List<String> formatSuccessMessage = new ArrayList<>();
        //数据格式无误 导入数据
        String peopleSuccessMessage = idwOfficerMainService.importPeople(updateSupport, operName);
        if (StringUtils.isNotBlank(peopleSuccessMessage)) {
            formatSuccessMessage.add(peopleSuccessMessage);
        }        //教育经历
        String peopleEducationSuccessMessage = idwPeopleEducationService.importEducation(updateSupport, operName);
        if (StringUtils.isNotBlank(peopleEducationSuccessMessage)) {
            formatSuccessMessage.add(peopleEducationSuccessMessage);
        }        //社交媒体账号
        String peopleSocialAccountSuccessMessage = idwPeopleSocialAccountService.importSocialAccount(updateSupport, operName);
        if (StringUtils.isNotBlank(peopleSocialAccountSuccessMessage)) {
            formatSuccessMessage.add(peopleSocialAccountSuccessMessage);
        }        //社交媒体
        String peopleSocialMediaSuccessMessage = idwPeopleSocialMediaService.importSocialMedia(updateSupport, operName);
        if (StringUtils.isNotBlank(peopleSocialMediaSuccessMessage)) {
            formatSuccessMessage.add(peopleSocialMediaSuccessMessage);
        }        //新闻报道
        String peopleNewsSuccessMessage = idwPeopleNewsService.importNews(updateSupport, operName, "news");
        if (StringUtils.isNotBlank(peopleNewsSuccessMessage)) {
            formatSuccessMessage.add(peopleNewsSuccessMessage);
        }        //工作经历
        String peopleWorkExperienceSuccessMessage = idwPeopleWorkExperienceService.importWorkExperience(updateSupport, operName);
        if (StringUtils.isNotBlank(peopleWorkExperienceSuccessMessage)) {
            formatSuccessMessage.add(peopleWorkExperienceSuccessMessage);
        }        //荣誉奖项
        String peopleHonorSuccessMessage = idwPeopleHonorService.importHonor(updateSupport, operName, "荣誉奖项");
        if (StringUtils.isNotBlank(peopleHonorSuccessMessage)) {
            formatSuccessMessage.add(peopleHonorSuccessMessage);
        }        //人员关系
        String peopleRelationshipSuccessMessage = idwPeopleRelationshipService.importRelationship(updateSupport, operName, "人员关系");
        if (StringUtils.isNotBlank(peopleRelationshipSuccessMessage)) {
            formatSuccessMessage.add(peopleRelationshipSuccessMessage);
        }        //治军理念
        String peopleArmyIdeaSuccessMessage = idwPeopleArmyIdeaService.importArmyIdea(updateSupport, operName);
        if (StringUtils.isNotBlank(peopleArmyIdeaSuccessMessage)) {
            formatSuccessMessage.add(peopleArmyIdeaSuccessMessage);
        }        //参与的项目
        String peopleAchievementSuccessMessage = idwPeopleAchievementService.importAchievement(updateSupport, operName, "参与的项目");
        if (StringUtils.isNotBlank(peopleAchievementSuccessMessage)) {
            formatSuccessMessage.add(peopleAchievementSuccessMessage);
        }        //图片视频
        String peopleMultimediaSuccessMessage = idwMultimediaService.importMultimedia(updateSupport, operName, "图片视频");
        if (StringUtils.isNotBlank(peopleMultimediaSuccessMessage)) {
            formatSuccessMessage.add(peopleMultimediaSuccessMessage);
        }
        if (formatSuccessMessage.size() < 1) {
            //错误Excel
            formatErrorMessage.add("模板错误,,");
            msgMap.put("status", false);
            msgMap.put("msg", formatErrorMessage);
            return msgMap;
        }

        msgMap.put("status", true);
        msgMap.put("msg", formatSuccessMessage);
        return msgMap;
    }

    /**
     * 根据人员编码导出政府军队官员
     */
    @RequiresPermissions("people:officer:export")
    @Log(title = "政府军队官员", businessType = BusinessType.EXPORT)
    @PostMapping("/exportExcel")
    @ResponseBody
    public AjaxResult exportExcel(String[] peopleCodes) {
        //压缩文件路径
        List<String> filePathList = new ArrayList<>();
        //Excel文件路径
        List<String> excelFilePath = new ArrayList<>();
        List<IdwOfficerMain> peopleList = idwOfficerMainService.selectByPeopleCodes(peopleCodes);
        List<String> peopleAvatarList = peopleList.stream().filter(p -> StringUtils.isNotBlank(p.getAvatar())).map(IdwOfficerMain::getAvatar).collect(Collectors.toList());
        //保存人员图片路径
        if (peopleAvatarList.size() > 0) {
            filePathList.addAll(peopleAvatarList);
        }
        ExcelUtil<IdwOfficerMain> peopleMainExcelUtil = new ExcelUtil<IdwOfficerMain>(IdwOfficerMain.class);
        String peopleFileName = (String) peopleMainExcelUtil.exportExcel(peopleList, "政府军队官员").get("msg");
        //获取Excel路径
        String peopleExcelFilePath = WebdpConfig.getDownloadPath() + peopleFileName;
        filePathList.add(peopleExcelFilePath);
        excelFilePath.add(peopleExcelFilePath);

        //教育经历
        List<IdwPeopleEducation> peopleEducationList = idwPeopleEducationService.selectByPeopleCodes(peopleCodes);
        if (peopleEducationList.size() > 0) {
            ExcelUtil<IdwPeopleEducation> peopleEducationExcelUtil = new ExcelUtil<IdwPeopleEducation>(IdwPeopleEducation.class);
            String peopleEducationFileName = (String) peopleEducationExcelUtil.exportExcel(peopleEducationList, "教育经历").get("msg");
            //获取Excel路径
            String peopleEducationExcelFilePath = WebdpConfig.getDownloadPath() + peopleEducationFileName;
            filePathList.add(peopleEducationExcelFilePath);
            excelFilePath.add(peopleEducationExcelFilePath);
        }

        //社交媒体账号
        List<IdwPeopleSocialAccount> peopleSocialAccountList = idwPeopleSocialAccountService.selectByPeopleCodes(peopleCodes);
        if (peopleSocialAccountList.size() > 0) {
            List<String> peopleSocialAccountAvatarList = peopleSocialAccountList.stream().filter(sa -> StringUtils.isNotBlank(sa.getAvatar())).map(IdwPeopleSocialAccount::getAvatar).collect(Collectors.toList());
            //保存社交媒体账号头像
            if (peopleSocialAccountAvatarList.size() > 0) {
                filePathList.addAll(peopleSocialAccountAvatarList);
            }
            ExcelUtil<IdwPeopleSocialAccount> peopleSocialAccountExcelUtil = new ExcelUtil<IdwPeopleSocialAccount>(IdwPeopleSocialAccount.class);
            String peopleSocialAccountFileName = (String) peopleSocialAccountExcelUtil.exportExcel(peopleSocialAccountList, "社交媒体账号").get("msg");
            //获取Excel路径
            String peopleSocialAccountExcelFilePath = WebdpConfig.getDownloadPath() + peopleSocialAccountFileName;
            filePathList.add(peopleSocialAccountExcelFilePath);
            excelFilePath.add(peopleSocialAccountExcelFilePath);
        }

        //社交媒体
        List<IdwPeopleSocialMedia> peopleSocialMediaList = idwPeopleSocialMediaService.selectByPeopleCodes(peopleCodes);
        if (peopleSocialMediaList.size() > 0) {
            ExcelUtil<IdwPeopleSocialMedia> peopleSocialMediaExcelUtil = new ExcelUtil<IdwPeopleSocialMedia>(IdwPeopleSocialMedia.class);
            String peopleSocialMediaFileName = (String) peopleSocialMediaExcelUtil.exportExcel(peopleSocialMediaList, "社交媒体").get("msg");
            //获取Excel路径
            String peopleSocialMediaExcelFilePath = WebdpConfig.getDownloadPath() + peopleSocialMediaFileName;
            filePathList.add(peopleSocialMediaExcelFilePath);
            excelFilePath.add(peopleSocialMediaExcelFilePath);
        }

        //新闻报道
        List<IdwPeopleNews> peopleNewsList = idwPeopleNewsService.selectByPeopleCodes(peopleCodes);
        if (peopleNewsList.size() > 0) {
            List<String> peopleNewsThumbnailList = peopleNewsList.stream().filter(n -> StringUtils.isNotBlank(n.getThumbnail())).map(IdwPeopleNews::getThumbnail).collect(Collectors.toList());
            //保存新闻报道缩略图
            if (peopleNewsThumbnailList.size() > 0) {
                filePathList.addAll(peopleNewsThumbnailList);
            }
            ExcelUtil<IdwPeopleNews> peopleNewsExcelUtil = new ExcelUtil<IdwPeopleNews>(IdwPeopleNews.class);
            String peopleNewsFileName = (String) peopleNewsExcelUtil.exportExcel(peopleNewsList, "新闻报道").get("msg");
            //获取Excel路径
            String peopleNewsExcelFilePath = WebdpConfig.getDownloadPath() + peopleNewsFileName;
            filePathList.add(peopleNewsExcelFilePath);
            excelFilePath.add(peopleNewsExcelFilePath);
        }

        //工作经历
        List<IdwPeopleWorkExperience> peopleWorkExperienceList = idwPeopleWorkExperienceService.selectByPeopleCodes(peopleCodes);
        if (peopleWorkExperienceList.size() > 0) {
            ExcelUtil<IdwPeopleWorkExperience> peopleWorkExperienceExcelUtil = new ExcelUtil<IdwPeopleWorkExperience>(IdwPeopleWorkExperience.class);
            String peopleWorkExperienceFileName = (String) peopleWorkExperienceExcelUtil.exportExcel(peopleWorkExperienceList, "工作经历").get("msg");
            //获取Excel路径
            String peopleWorkExperienceExcelFilePath = WebdpConfig.getDownloadPath() + peopleWorkExperienceFileName;
            filePathList.add(peopleWorkExperienceExcelFilePath);
            excelFilePath.add(peopleWorkExperienceExcelFilePath);
        }

        //荣誉奖项
        List<IdwPeopleHonor> peopleHonorList = idwPeopleHonorService.selectByPeopleCodes(peopleCodes);
        if (peopleHonorList.size() > 0) {
            ExcelUtil<IdwPeopleHonor> peopleHonorExcelUtil = new ExcelUtil<IdwPeopleHonor>(IdwPeopleHonor.class);
            String peopleHonorFileName = (String) peopleHonorExcelUtil.exportExcel(peopleHonorList, "荣誉奖项").get("msg");
            //获取Excel路径
            String peopleHonorExcelFilePath = WebdpConfig.getDownloadPath() + peopleHonorFileName;
            filePathList.add(peopleHonorExcelFilePath);
            excelFilePath.add(peopleHonorExcelFilePath);
        }

        //人员关系
        List<IdwPeopleRelationship> peopleRelationshipList = idwPeopleRelationshipService.selectByPeopleCodes(peopleCodes);
        if (peopleRelationshipList.size() > 0) {
            List<String> peopleRelationshipAvatarList = peopleRelationshipList.stream().filter(r -> StringUtils.isNotBlank(r.getRelatedPeopleAvatar())).map(IdwPeopleRelationship::getRelatedPeopleAvatar).collect(Collectors.toList());
            //保存关系人员头像
            if (peopleRelationshipAvatarList.size() > 0) {
                filePathList.addAll(peopleRelationshipAvatarList);
            }
            ExcelUtil<IdwPeopleRelationship> peopleRelationshipExcelUtil = new ExcelUtil<IdwPeopleRelationship>(IdwPeopleRelationship.class);
            String peopleRelationshipFileName = (String) peopleRelationshipExcelUtil.exportExcel(peopleRelationshipList, "人员关系 ").get("msg");
            //获取Excel路径
            String peopleRelationshipExcelFilePath = WebdpConfig.getDownloadPath() + peopleRelationshipFileName;
            filePathList.add(peopleRelationshipExcelFilePath);
            excelFilePath.add(peopleRelationshipExcelFilePath);
        }

        //治军理念
        List<IdwPeopleArmyIdea> peopleArmyIdeaList = idwPeopleArmyIdeaService.selectByPeopleCodes(peopleCodes);
        if (peopleArmyIdeaList.size() > 0) {
            ExcelUtil<IdwPeopleArmyIdea> peopleArmyIdeaExcelUtil = new ExcelUtil<IdwPeopleArmyIdea>(IdwPeopleArmyIdea.class);
            String peopleArmyIdeaFileName = (String) peopleArmyIdeaExcelUtil.exportExcel(peopleArmyIdeaList, "治军理念").get("msg");
            //获取Excel路径
            String peopleArmyIdeaExcelFilePath = WebdpConfig.getDownloadPath() + peopleArmyIdeaFileName;
            filePathList.add(peopleArmyIdeaExcelFilePath);
            excelFilePath.add(peopleArmyIdeaExcelFilePath);
        }

        //参与的项目
        List<IdwPeopleAchievement> peopleAchievementList = idwPeopleAchievementService.selectByPeopleCodes(peopleCodes, "主持项目");
        if (peopleAchievementList.size() > 0) {
            List<String> peopleAchievementStoragePathList = peopleAchievementList.stream().filter(a -> StringUtils.isNotBlank(a.getStoragePath())).map(IdwPeopleAchievement::getStoragePath).collect(Collectors.toList());
            //保存项目附件
            if (peopleAchievementStoragePathList.size() > 0) {
                filePathList.addAll(peopleAchievementStoragePathList);
            }
            ExcelUtil<IdwPeopleAchievement> peopleAchievementExcelUtil = new ExcelUtil<IdwPeopleAchievement>(IdwPeopleAchievement.class);
            String peopleAchievementFileName = (String) peopleAchievementExcelUtil.exportExcel(peopleAchievementList, "参与的项目 ").get("msg");
            //获取Excel路径
            String peopleAchievementExcelFilePath = WebdpConfig.getDownloadPath() + peopleAchievementFileName;
            filePathList.add(peopleAchievementExcelFilePath);
            excelFilePath.add(peopleAchievementExcelFilePath);
        }

        //图片视频
        List<IdwMultimedia> peopleMultimediaList = idwMultimediaService.selectByAssociationCodes(peopleCodes, "personnel");
        if (peopleMultimediaList.size() > 0) {
            List<String> peopleMultimediaStoragePathList = peopleMultimediaList.stream().filter(m -> StringUtils.isNotBlank(m.getStoragePath())).map(IdwMultimedia::getStoragePath).collect(Collectors.toList());
            //保存图片视频
            if (peopleMultimediaStoragePathList.size() > 0) {
                filePathList.addAll(peopleMultimediaStoragePathList);
            }
            List<String> peopleMultimediaThumbnailList = peopleMultimediaList.stream().filter(m -> StringUtils.isNotBlank(m.getThumbnail())).map(IdwMultimedia::getThumbnail).collect(Collectors.toList());
            //保存缩略图
            if (peopleMultimediaThumbnailList.size() > 0) {
                filePathList.addAll(peopleMultimediaThumbnailList);
            }
            ExcelUtil<IdwMultimedia> peopleMultimediaExcelUtil = new ExcelUtil<IdwMultimedia>(IdwMultimedia.class);
            String peopleMultimediaFileName = (String) peopleMultimediaExcelUtil.exportExcel(peopleMultimediaList, "图片视频 ").get("msg");
            //获取Excel路径
            String peopleMultimediaExcelFilePath = WebdpConfig.getDownloadPath() + peopleMultimediaFileName;
            filePathList.add(peopleMultimediaExcelFilePath);
            excelFilePath.add(peopleMultimediaExcelFilePath);
        }

        //拼接压缩包路径
        String zipFileName = UUID.randomUUID().toString() + "_政府军队官员.zip";
        String zipFilePath = WebdpConfig.getDownloadPath() + "/" + zipFileName;
        //将文件压缩
        FileUtils.multifileCompressionZip(filePathList, zipFilePath);
        //删除Excel
        FileUtils.batchDeleteFile(excelFilePath);
        return AjaxResult.success(zipFileName);
    }
}
