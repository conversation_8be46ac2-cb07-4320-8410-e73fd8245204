<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lirong.project.member.mapper.IdwProjectMemberMapper">
    
    <resultMap type="com.lirong.project.member.domain.IdwProjectMember" id="IdwProjectMemberResult">
        <result property="memberId"    column="member_id"    />
        <result property="projectId"    column="project_id"    />
        <result property="peopleCode"    column="people_code"    />
        <result property="name"    column="name"    />
        <result property="avatar"    column="avatar"    />
        <result property="role"    column="role"    />
        <result property="isDelete"    column="is_delete"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
    </resultMap>

    <sql id="selectIdwProjectMemberVo">
        SELECT
            member_id,
            project_id,
            people_code,
            name,
            avatar,
            role
        FROM
            idw_project_member
    </sql>

    <select id="selectIdwProjectMemberList" parameterType="com.lirong.project.member.domain.IdwProjectMember" resultMap="IdwProjectMemberResult">
        <include refid="selectIdwProjectMemberVo"/>
        <where>
            is_delete = 0
            <if test="projectId != null "> and project_id = #{projectId}</if>
            <if test="name != null  and name != ''"> and name like concat('%', #{name}, '%')</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="role != null  and role != ''"> and role like concat('%', #{role}, '%')</if>
        </where>
    </select>
    
    <select id="selectIdwProjectMemberById" parameterType="Long" resultMap="IdwProjectMemberResult">
        <include refid="selectIdwProjectMemberVo"/>
        where member_id = #{memberId} and is_delete = 0
    </select>

    <!--获取文件路径-->
    <select id="selectAllFilePath" resultType="java.lang.String">
        SELECT
            avatar
        FROM
            idw_project_member
        WHERE
            is_delete = 0
            AND avatar IS NOT NULL
            AND avatar != ''
    </select>

    <insert id="insertIdwProjectMember" parameterType="com.lirong.project.member.domain.IdwProjectMember" useGeneratedKeys="true" keyProperty="memberId">
        insert into idw_project_member
        <trim prefix="(" suffix=")" suffixOverrides=",">
            is_delete,
            <if test="projectId != null">project_id,</if>
            <if test="peopleCode != null and peopleCode != ''">people_code,</if>
            <if test="name != null and name != ''">name,</if>
            <if test="avatar != null">avatar,</if>
            <if test="role != null">role,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            0,
            <if test="projectId != null">#{projectId},</if>
            <if test="peopleCode != null and peopleCode != ''">#{peopleCode},</if>
            <if test="name != null and name != ''">#{name},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="role != null">#{role},</if>
            <if test="createBy != null and createBy != ''">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateIdwProjectMember" parameterType="com.lirong.project.member.domain.IdwProjectMember">
        update idw_project_member
        <trim prefix="SET" suffixOverrides=",">
            <if test="projectId != null">project_id = #{projectId},</if>
            <if test="peopleCode != null">people_code = #{peopleCode},</if>
            <if test="name != null">name = #{name},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="role != null">role = #{role},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where member_id = #{memberId}
    </update>

    <update id="deleteIdwProjectMemberByIds">
        update idw_project_member
        SET update_by = #{loginName},
        update_time = sysdate(),
        IS_DELETE = 1
        WHERE member_id in
        <foreach item="memberId" collection="memberIds" open="(" separator="," close=")">
            #{memberId}
        </foreach>
    </update>

</mapper>