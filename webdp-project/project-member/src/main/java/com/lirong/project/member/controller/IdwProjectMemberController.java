package com.lirong.project.member.controller;

import java.util.List;

import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import com.lirong.common.annotation.Log;
import com.lirong.common.enums.BusinessType;
import com.lirong.project.member.domain.IdwProjectMember;
import com.lirong.project.member.service.IdwProjectMemberService;
import com.lirong.common.core.controller.BaseController;
import com.lirong.common.core.domain.AjaxResult;
import com.lirong.common.utils.poi.ExcelUtil;
import com.lirong.common.core.page.TableDataInfo;

/**
 * 项目成员Controller
 *
 * <AUTHOR>
 * @date 2021-06-25
 */
@Controller
@RequestMapping("/project/member")
public class IdwProjectMemberController extends BaseController {
    private String prefix = "project/member";

    @Autowired
    private IdwProjectMemberService idwProjectMemberService;

    @RequiresPermissions("project:member:view")
    @GetMapping("/{projectId}")
    public String member(@PathVariable("projectId") Long projectId, ModelMap mmap) {
        mmap.put("projectId", projectId);
        return prefix + "/member";
    }

    /**
     * 查询项目成员列表
     */
    @RequiresPermissions("project:member:list")
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(IdwProjectMember idwProjectMember) {
        startPage();
        List<IdwProjectMember> list = idwProjectMemberService.selectIdwProjectMemberList(idwProjectMember);
        return getDataTable(list);
    }

    /**
     * 新增项目成员
     */
    @GetMapping("/add/{projectId}")
    public String add(@PathVariable("projectId") Long projectId, ModelMap mmap) {
        mmap.put("projectId", projectId);
        return prefix + "/add";
    }

    /**
     * 新增保存项目成员
     */
    @RequiresPermissions("project:member:add")
    @Log(title = "项目成员", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(IdwProjectMember idwProjectMember) {
        return toAjax(idwProjectMemberService.insertIdwProjectMember(idwProjectMember));
    }

    /**
     * 修改项目成员
     */
    @GetMapping("/edit/{memberId}")
    public String edit(@PathVariable("memberId") Long memberId, ModelMap mmap) {
        IdwProjectMember idwProjectMember = idwProjectMemberService.selectIdwProjectMemberById(memberId);
        mmap.put("idwProjectMember", idwProjectMember);
        return prefix + "/edit";
    }

    /**
     * 修改保存项目成员
     */
    @RequiresPermissions("project:member:edit")
    @Log(title = "项目成员", businessType = BusinessType.UPDATE)
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(IdwProjectMember idwProjectMember) {
        return toAjax(idwProjectMemberService.updateIdwProjectMember(idwProjectMember));
    }

    /**
     * 删除项目成员
     */
    @RequiresPermissions("project:member:remove")
    @Log(title = "项目成员", businessType = BusinessType.DELETE)
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        return toAjax(idwProjectMemberService.deleteIdwProjectMemberByIds(ids));
    }

    /**
     * 导出项目成员列表
     */
    @RequiresPermissions("project:member:export")
    @Log(title = "项目成员", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(IdwProjectMember idwProjectMember) {
        List<IdwProjectMember> list = idwProjectMemberService.selectIdwProjectMemberList(idwProjectMember);
        ExcelUtil<IdwProjectMember> util = new ExcelUtil<IdwProjectMember>(IdwProjectMember.class);
        return util.exportExcel(list, "member");
    }
}
