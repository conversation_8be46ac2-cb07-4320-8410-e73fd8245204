package com.lirong.system.service.impl;

import com.lirong.common.utils.StringUtils;
import com.lirong.system.domain.SysTranslate;
import com.lirong.system.domain.Translate;
import com.lirong.system.mapper.SysTranslateMapper;
import com.lirong.system.service.ISysTranslateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SysTranslateServiceImpl implements ISysTranslateService {

    @Autowired
    private SysTranslateMapper translateMapper;

    @Override
    public List<SysTranslate> getPendingData(Translate translate) {
        return translateMapper.getPendingResult(translate);
    }

    @Override
    public void saveTranslateResult(Translate translate) {
        translateMapper.saveTranslateResultList(translate);
    }

    @Override
    public void saveTranslateResult(String tableName, String primaryKey, String id, String translatedField, String translated) {
        if (StringUtils.isNotBlank(translated)) {
            translateMapper.saveTranslateResult(tableName, primaryKey, id, translatedField, translated);
        }
    }

}
