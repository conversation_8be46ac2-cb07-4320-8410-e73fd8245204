# 项目相关配置
webdp:
  # 名称
  name: Webdp
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/webdp/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: G:/webdp/uploadPath
  # 获取ip地址开关
  addressEnabled: false

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为80
  port: 90
  servlet:
    # 应用的访问路径
    context-path: /
  # Undertow服务器配置
  undertow:
    # 设置IO线程数, 它主要执行非阻塞的任务,它们会负责多个连接, 默认设置每个CPU核心一个线程
    io-threads: 16
    # 阻塞任务线程池, 当执行类似servlet请求阻塞操作, undertow会从这个线程池中取得线程,它的值设置取决于系统的负载
    worker-threads: 256
    # 以下的配置会影响buffer,这些buffer会用于服务器连接的IO操作,有点类似netty的池化内存管理
    buffer-size: 1024
    # 是否分配的直接内存
    direct-buffers: true
    # HTTP POST请求最大的大小
    max-http-post-size: 2048MB
    # 设置临时目录
    accesslog:
      # 访问日志目录，使用相对路径
      dir: ./logs
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    # 打印日志级别 debug error
    com.lirong: debug
    org.springframework: warn

# 用户配置
user:
  password:
    # 密码错误{maxRetryCount}次锁定10分钟
    maxRetryCount: 5

# Spring配置
spring:
  http:
    encoding:
      force: true
      charset: UTF-8
      enabled: true
  # 模板引擎
  thymeleaf:
    mode: HTML
    encoding: utf-8
    # 禁用缓存
    cache: false
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
    encoding: UTF-8
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
  profiles:  #使用配置文件名称 达梦 dm MySQL druid
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  1024MB
       # 设置总上传的文件大小
       max-request-size:  2048MB
       # 设置临时文件存储位置，解决Undertow临时目录不存在的问题
       location: ${java.io.tmpdir}/webdp-upload
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true

# MyBatis
mybatis:
    # 搜索指定包别名
    typeAliasesPackage: com.lirong.**.domain
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml

mybatis-plus:
    global-config:
      db-config:
        id-type: ID_WORKER
        field-strategy: not_empty
        #驼峰下划线转换
        column-underline: true
        #逻辑删除配置
        logic-delete-value: 0
        logic-not-delete-value: 1
        db-type: mysql
      refresh: false
    configuration:
      map-underscore-to-camel-case: true
      cache-enabled: false

# PageHelper分页插件
pagehelper: 
  helperDialect: mysql
  reasonable: true
  supportMethodsArguments: true
  params: count=countSql

# Shiro
shiro:
  user:
    # 登录地址
    loginUrl: /login
    # 权限认证失败地址
    unauthorizedUrl: /unauth
    # 首页地址
    indexUrl: /index
    # 验证码开关
    captchaEnabled: false
    # 验证码类型 math 数组计算 char 字符
    captchaType: math
  cookie:
    # 设置Cookie的域名 默认空，即当前访问的域名
    domain: 
    # 设置cookie的有效访问路径
    path: /
    # 设置HttpOnly属性
    httpOnly: true
    # 设置Cookie的过期时间，天为单位
    maxAge: 30
    # 设置密钥，务必保持唯一性（生成方式，直接拷贝到main运行即可）KeyGenerator keygen = KeyGenerator.getInstance("AES"); SecretKey deskey = keygen.generateKey(); System.out.println(Base64.encodeToString(deskey.getEncoded()));
    cipherKey: hwXOCRDp14HZYFtThsZwkw==
  session:
    # Session超时时间，-1代表永不过期（默认30分钟）
    expireTime: -1
    # 同步session到数据库的周期（默认1分钟）
    dbSyncPeriod: 1
    # 相隔多久检查一次session的有效性，默认就是10分钟
    validationInterval: 10
    # 同一个用户最大会话数，比如2的意思是同一个账号允许最多同时两个人登录（默认-1不限制）
    maxSession: -1
    # 踢出之前登录的/之后登录的用户，默认踢出之前登录的用户
    kickoutAfter: false

# 防止XSS攻击
xss: 
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true

# AI服务配置
ai:
  siliconflow:
    # 本地LLM API地址
    api-url: https://api.siliconflow.cn/v1/chat/completions
    # API密钥（本地LLM可能不需要，但保留配置）
    api-key: sk-moazahcmdhanlbtvpfkwxwiazishefcnkroyotqaglomnrkh
    # 模型名称
    model: deepseek-ai/DeepSeek-V3
    # 最大token数
    max-tokens: 4000
    # 温度参数
    temperature: 0.1
    # 连接超时时间（毫秒）
    connect-timeout: 60000
    # 读取超时时间（毫秒）
    read-timeout: 300000
