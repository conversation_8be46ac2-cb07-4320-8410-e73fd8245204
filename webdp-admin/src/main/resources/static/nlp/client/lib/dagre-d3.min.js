!function(t){if("object"==typeof exports&&"undefined"!=typeof module)module.exports=t();else if("function"==typeof define&&define.amd)define([],t);else{var n;n="undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:this,n.dagreD3=t()}}(function(){var t;return function n(t,e,r){function i(a,u){if(!e[a]){if(!t[a]){var c="function"==typeof require&&require;if(!u&&c)return c(a,!0);if(o)return o(a,!0);var s=new Error("Cannot find module '"+a+"'");throw s.code="MODULE_NOT_FOUND",s}var f=e[a]={exports:{}};t[a][0].call(f.exports,function(n){var e=t[a][1][n];return i(e?e:n)},f,f.exports,n,t,e,r)}return e[a].exports}for(var o="function"==typeof require&&require,a=0;a<r.length;a++)i(r[a]);return i}({1:[function(t,n,e){/**
     * @license
     * Copyright (c) 2012-2013 Chris Pettitt
     *
     * Permission is hereby granted, free of charge, to any person obtaining a copy
     * of this software and associated documentation files (the "Software"), to deal
     * in the Software without restriction, including without limitation the rights
     * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
     * copies of the Software, and to permit persons to whom the Software is
     * furnished to do so, subject to the following conditions:
     *
     * The above copyright notice and this permission notice shall be included in
     * all copies or substantial portions of the Software.
     *
     * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
     * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
     * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
     * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
     * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
     * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
     * THE SOFTWARE.
     */
    n.exports={graphlib:t("./lib/graphlib"),dagre:t("./lib/dagre"),intersect:t("./lib/intersect"),render:t("./lib/render"),util:t("./lib/util"),version:t("./lib/version")}},{"./lib/dagre":8,"./lib/graphlib":9,"./lib/intersect":10,"./lib/render":25,"./lib/util":27,"./lib/version":28}],2:[function(t,n,e){function r(t,n,e,r){var i=t.append("marker").attr("id",n).attr("viewBox","0 0 10 10").attr("refX",9).attr("refY",5).attr("markerUnits","strokeWidth").attr("markerWidth",8).attr("markerHeight",6).attr("orient","auto"),o=i.append("path").attr("d","M 0 0 L 10 5 L 0 10 z").style("stroke-width",1).style("stroke-dasharray","1,0");a.applyStyle(o,e[r+"Style"]),e[r+"Class"]&&o.attr("class",e[r+"Class"])}function i(t,n,e,r){var i=t.append("marker").attr("id",n).attr("viewBox","0 0 10 10").attr("refX",9).attr("refY",5).attr("markerUnits","strokeWidth").attr("markerWidth",8).attr("markerHeight",6).attr("orient","auto"),o=i.append("path").attr("d","M 0 0 L 10 5 L 0 10 L 4 5 z").style("stroke-width",1).style("stroke-dasharray","1,0");a.applyStyle(o,e[r+"Style"]),e[r+"Class"]&&o.attr("class",e[r+"Class"])}function o(t,n,e,r){var i=t.append("marker").attr("id",n).attr("viewBox","0 0 10 10").attr("refX",9).attr("refY",5).attr("markerUnits","strokeWidth").attr("markerWidth",8).attr("markerHeight",6).attr("orient","auto"),o=i.append("path").attr("d","M 0 5 L 10 5").style("stroke-width",1).style("stroke-dasharray","1,0");a.applyStyle(o,e[r+"Style"]),e[r+"Class"]&&o.attr("class",e[r+"Class"])}var a=t("./util");n.exports={"default":r,normal:r,vee:i,undirected:o}},{"./util":27}],3:[function(t,n,e){function r(t,n){var e=n.nodes().filter(function(t){return i.isSubgraph(n,t)}),r=t.selectAll("g.cluster").data(e,function(t){return t});return r.selectAll("*").remove(),r.enter().append("g").attr("class","cluster").attr("id",function(t){var e=n.node(t);return e.id}).style("opacity",0),i.applyTransition(r,n).style("opacity",1),r.each(function(t){var e=n.node(t),r=d3.select(this);d3.select(this).append("rect");var i=r.append("g").attr("class","label");o(i,e,e.clusterLabelPos)}),r.selectAll("rect").each(function(t){var e=n.node(t),r=d3.select(this);i.applyStyle(r,e.style)}),i.applyTransition(r.exit(),n).style("opacity",0).remove(),r}var i=t("./util"),o=t("./label/add-label");n.exports=r},{"./label/add-label":18,"./util":27}],4:[function(t,n,e){"use strict";function r(t,n){var e=t.selectAll("g.edgeLabel").data(n.edges(),function(t){return a.edgeToId(t)}).classed("update",!0);return e.selectAll("*").remove(),e.enter().append("g").classed("edgeLabel",!0).style("opacity",0),e.each(function(t){var e=n.edge(t),r=o(u.select(this),n.edge(t),0,0).classed("label",!0),a=r.node().getBBox();e.labelId&&r.attr("id",e.labelId),i.has(e,"width")||(e.width=a.width),i.has(e,"height")||(e.height=a.height)}),a.applyTransition(e.exit(),n).style("opacity",0).remove(),e}var i=t("./lodash"),o=t("./label/add-label"),a=t("./util"),u=t("./d3");n.exports=r},{"./d3":7,"./label/add-label":18,"./lodash":21,"./util":27}],5:[function(t,n,e){"use strict";function r(t,n,e){var r=t.selectAll("g.edgePath").data(n.edges(),function(t){return h.edgeToId(t)}).classed("update",!0);return c(r,n),s(r,n),h.applyTransition(r,n).style("opacity",1),r.each(function(t){var e=d.select(this),r=n.edge(t);r.elem=this,r.id&&e.attr("id",r.id),h.applyClass(e,r["class"],(e.classed("update")?"update ":"")+"edgePath")}),r.selectAll("path.path").each(function(t){var e=n.edge(t);e.arrowheadId=f.uniqueId("arrowhead");var r=d.select(this).attr("marker-end",function(){return"url("+i(location.href,e.arrowheadId)+")"}).style("fill","none");h.applyTransition(r,n).attr("d",function(t){return o(n,t)}),h.applyStyle(r,e.style)}),r.selectAll("defs *").remove(),r.selectAll("defs").each(function(t){var r=n.edge(t),i=e[r.arrowhead];i(d.select(this),r.arrowheadId,r,"arrowhead")}),r}function i(t,n){var e=t.split("#")[0];return e+"#"+n}function o(t,n){var e=t.edge(n),r=t.node(n.v),i=t.node(n.w),o=e.points.slice(1,e.points.length-1);return o.unshift(l(r,o[0])),o.push(l(i,o[o.length-1])),a(e,o)}function a(t,n){var e=d.svg.line().x(function(t){return t.x}).y(function(t){return t.y});return f.has(t,"lineInterpolate")&&e.interpolate(t.lineInterpolate),f.has(t,"lineTension")&&e.tension(Number(t.lineTension)),e(n)}function u(t){var n=t.getBBox(),e=t.ownerSVGElement.getScreenCTM().inverse().multiply(t.getScreenCTM()).translate(n.width/2,n.height/2);return{x:e.e,y:e.f}}function c(t,n){var e=t.enter().append("g").attr("class","edgePath").style("opacity",0);e.append("path").attr("class","path").attr("d",function(t){var e=n.edge(t),r=n.node(t.v).elem,i=f.range(e.points.length).map(function(){return u(r)});return a(e,i)}),e.append("defs")}function s(t,n){var e=t.exit();h.applyTransition(e,n).style("opacity",0).remove(),h.applyTransition(e.select("path.path"),n).attr("d",function(t){var e=n.node(t.v);if(e){var r=f.range(this.getTotalLength()).map(function(){return e});return a({},r)}return d.select(this).attr("d")})}var f=t("./lodash"),l=t("./intersect/intersect-node"),h=t("./util"),d=t("./d3");n.exports=r},{"./d3":7,"./intersect/intersect-node":14,"./lodash":21,"./util":27}],6:[function(t,n,e){"use strict";function r(t,n,e){var r=n.nodes().filter(function(t){return!a.isSubgraph(n,t)}),c=t.selectAll("g.node").data(r,function(t){return t}).classed("update",!0);return c.selectAll("*").remove(),c.enter().append("g").attr("class","node").style("opacity",0),c.each(function(t){var r=n.node(t),c=u.select(this),s=c.append("g").attr("class","label"),f=o(s,r),l=e[r.shape],h=i.pick(f.node().getBBox(),"width","height");r.elem=this,r.id&&c.attr("id",r.id),r.labelId&&s.attr("id",r.labelId),a.applyClass(c,r["class"],(c.classed("update")?"update ":"")+"node"),i.has(r,"width")&&(h.width=r.width),i.has(r,"height")&&(h.height=r.height),h.width+=r.paddingLeft+r.paddingRight,h.height+=r.paddingTop+r.paddingBottom,s.attr("transform","translate("+(r.paddingLeft-r.paddingRight)/2+","+(r.paddingTop-r.paddingBottom)/2+")");var d=l(u.select(this),h,r);a.applyStyle(d,r.style);var p=d.node().getBBox();r.width=p.width,r.height=p.height}),a.applyTransition(c.exit(),n).style("opacity",0).remove(),c}var i=t("./lodash"),o=t("./label/add-label"),a=t("./util"),u=t("./d3");n.exports=r},{"./d3":7,"./label/add-label":18,"./lodash":21,"./util":27}],7:[function(t,n,e){n.exports=window.d3},{}],8:[function(t,n,e){var r;if(t)try{r=t("dagre")}catch(i){}r||(r=window.dagre),n.exports=r},{dagre:29}],9:[function(t,n,e){var r;if(t)try{r=t("graphlib")}catch(i){}r||(r=window.graphlib),n.exports=r},{graphlib:59}],10:[function(t,n,e){n.exports={node:t("./intersect-node"),circle:t("./intersect-circle"),ellipse:t("./intersect-ellipse"),polygon:t("./intersect-polygon"),rect:t("./intersect-rect")}},{"./intersect-circle":11,"./intersect-ellipse":12,"./intersect-node":14,"./intersect-polygon":15,"./intersect-rect":16}],11:[function(t,n,e){function r(t,n,e){return i(t,n,n,e)}var i=t("./intersect-ellipse");n.exports=r},{"./intersect-ellipse":12}],12:[function(t,n,e){function r(t,n,e,r){var i=t.x,o=t.y,a=i-r.x,u=o-r.y,c=Math.sqrt(n*n*u*u+e*e*a*a),s=Math.abs(n*e*a/c);r.x<i&&(s=-s);var f=Math.abs(n*e*u/c);return r.y<o&&(f=-f),{x:i+s,y:o+f}}n.exports=r},{}],13:[function(t,n,e){function r(t,n,e,r){var o,a,u,c,s,f,l,h,d,p,g,v,y,_,m;return o=n.y-t.y,u=t.x-n.x,s=n.x*t.y-t.x*n.y,d=o*e.x+u*e.y+s,p=o*r.x+u*r.y+s,0!==d&&0!==p&&i(d,p)||(a=r.y-e.y,c=e.x-r.x,f=r.x*e.y-e.x*r.y,l=a*t.x+c*t.y+f,h=a*n.x+c*n.y+f,0!==l&&0!==h&&i(l,h)||(g=o*c-a*u,0===g))?void 0:(v=Math.abs(g/2),y=u*f-c*s,_=0>y?(y-v)/g:(y+v)/g,y=a*s-o*f,m=0>y?(y-v)/g:(y+v)/g,{x:_,y:m})}function i(t,n){return t*n>0}n.exports=r},{}],14:[function(t,n,e){function r(t,n){return t.intersect(n)}n.exports=r},{}],15:[function(t,n,e){function r(t,n,e){var r=t.x,o=t.y,a=[],u=Number.POSITIVE_INFINITY,c=Number.POSITIVE_INFINITY;n.forEach(function(t){u=Math.min(u,t.x),c=Math.min(c,t.y)});for(var s=r-t.width/2-u,f=o-t.height/2-c,l=0;l<n.length;l++){var h=n[l],d=n[l<n.length-1?l+1:0],p=i(t,e,{x:s+h.x,y:f+h.y},{x:s+d.x,y:f+d.y});p&&a.push(p)}return a.length?(a.length>1&&a.sort(function(t,n){var r=t.x-e.x,i=t.y-e.y,o=Math.sqrt(r*r+i*i),a=n.x-e.x,u=n.y-e.y,c=Math.sqrt(a*a+u*u);return c>o?-1:o===c?0:1}),a[0]):(console.log("NO INTERSECTION FOUND, RETURN NODE CENTER",t),t)}var i=t("./intersect-line");n.exports=r},{"./intersect-line":13}],16:[function(t,n,e){function r(t,n){var e,r,i=t.x,o=t.y,a=n.x-i,u=n.y-o,c=t.width/2,s=t.height/2;return Math.abs(u)*c>Math.abs(a)*s?(0>u&&(s=-s),e=0===u?0:s*a/u,r=s):(0>a&&(c=-c),e=c,r=0===a?0:c*u/a),{x:i+e,y:o+r}}n.exports=r},{}],17:[function(t,n,e){function r(t,n){var e=t.append("foreignObject").attr("width","100000"),r=e.append("xhtml:div");r.attr("xmlns","http://www.w3.org/1999/xhtml");var o=n.label;switch(typeof o){case"function":r.insert(o);break;case"object":r.insert(function(){return o});break;default:r.html(o)}i.applyStyle(r,n.labelStyle),r.style("display","inline-block"),r.style("white-space","nowrap");var a=r[0][0].getBoundingClientRect();return e.attr("width",a.width).attr("height",a.height),e}var i=t("../util");n.exports=r},{"../util":27}],18:[function(t,n,e){function r(t,n,e){var r=n.label,u=t.append("g");"svg"===n.labelType?a(u,n):"string"!=typeof r||"html"===n.labelType?o(u,n):i(u,n);var c,s=u.node().getBBox();switch(e){case"top":c=-n.height/2;break;case"bottom":c=n.height/2-s.height;break;default:c=-s.height/2}return u.attr("transform","translate("+-s.width/2+","+c+")"),u}var i=t("./add-text-label"),o=t("./add-html-label"),a=t("./add-svg-label");n.exports=r},{"./add-html-label":17,"./add-svg-label":19,"./add-text-label":20}],19:[function(t,n,e){function r(t,n){var e=t;return e.node().appendChild(n.label),i.applyStyle(e,n.labelStyle),e}var i=t("../util");n.exports=r},{"../util":27}],20:[function(t,n,e){function r(t,n){for(var e=t.append("text"),r=i(n.label).split("\n"),a=0;a<r.length;a++)e.append("tspan").attr("xml:space","preserve").attr("dy","1em").attr("x","1").text(r[a]);return o.applyStyle(e,n.labelStyle),e}function i(t){for(var n,e="",r=!1,i=0;i<t.length;++i)if(n=t[i],r){switch(n){case"n":e+="\n";break;default:e+=n}r=!1}else"\\"===n?r=!0:e+=n;return e}var o=t("../util");n.exports=r},{"../util":27}],21:[function(t,n,e){var r;if(t)try{r=t("lodash")}catch(i){}r||(r=window._),n.exports=r},{lodash:79}],22:[function(t,n,e){"use strict";function r(t,n){function e(t){var e=n.node(t);return"translate("+e.x+","+e.y+")"}var r=t.filter(function(){return!o.select(this).classed("update")});r.attr("transform",e),i.applyTransition(t,n).style("opacity",1).attr("transform",e),i.applyTransition(r.selectAll("rect"),n).attr("width",function(t){return n.node(t).width}).attr("height",function(t){return n.node(t).height}).attr("x",function(t){var e=n.node(t);return-e.width/2}).attr("y",function(t){var e=n.node(t);return-e.height/2})}var i=t("./util"),o=t("./d3");n.exports=r},{"./d3":7,"./util":27}],23:[function(t,n,e){"use strict";function r(t,n){function e(t){var e=n.edge(t);return a.has(e,"x")?"translate("+e.x+","+e.y+")":""}var r=t.filter(function(){return!o.select(this).classed("update")});r.attr("transform",e),i.applyTransition(t,n).style("opacity",1).attr("transform",e)}var i=t("./util"),o=t("./d3"),a=t("./lodash");n.exports=r},{"./d3":7,"./lodash":21,"./util":27}],24:[function(t,n,e){"use strict";function r(t,n){function e(t){var e=n.node(t);return"translate("+e.x+","+e.y+")"}var r=t.filter(function(){return!o.select(this).classed("update")});r.attr("transform",e),i.applyTransition(t,n).style("opacity",1).attr("transform",e)}var i=t("./util"),o=t("./d3");n.exports=r},{"./d3":7,"./util":27}],25:[function(t,n,e){function r(){var n=t("./create-nodes"),e=t("./create-clusters"),r=t("./create-edge-labels"),u=t("./create-edge-paths"),s=t("./position-nodes"),f=t("./position-edge-labels"),l=t("./position-clusters"),h=t("./shapes"),d=t("./arrows"),p=function(t,p){i(p);var g=a(t,"output"),v=a(g,"clusters"),y=a(g,"edgePaths"),_=r(a(g,"edgeLabels"),p),m=n(a(g,"nodes"),p,h);c(p),s(m,p),f(_,p),u(y,p,d);var w=e(v,p);l(w,p),o(p)};return p.createNodes=function(t){return arguments.length?(n=t,p):n},p.createClusters=function(t){return arguments.length?(e=t,p):e},p.createEdgeLabels=function(t){return arguments.length?(r=t,p):r},p.createEdgePaths=function(t){return arguments.length?(u=t,p):u},p.shapes=function(t){return arguments.length?(h=t,p):h},p.arrows=function(t){return arguments.length?(d=t,p):d},p}function i(t){t.nodes().forEach(function(n){var e=t.node(n);u.has(e,"label")||t.children(n).length||(e.label=n),u.has(e,"paddingX")&&u.defaults(e,{paddingLeft:e.paddingX,paddingRight:e.paddingX}),u.has(e,"paddingY")&&u.defaults(e,{paddingTop:e.paddingY,paddingBottom:e.paddingY}),u.has(e,"padding")&&u.defaults(e,{paddingLeft:e.padding,paddingRight:e.padding,paddingTop:e.padding,paddingBottom:e.padding}),u.defaults(e,s),u.each(["paddingLeft","paddingRight","paddingTop","paddingBottom"],function(t){e[t]=Number(e[t])}),u.has(e,"width")&&(e._prevWidth=e.width),u.has(e,"height")&&(e._prevHeight=e.height)}),t.edges().forEach(function(n){var e=t.edge(n);u.has(e,"label")||(e.label=""),u.defaults(e,f)})}function o(t){u.each(t.nodes(),function(n){var e=t.node(n);u.has(e,"_prevWidth")?e.width=e._prevWidth:delete e.width,u.has(e,"_prevHeight")?e.height=e._prevHeight:delete e.height,delete e._prevWidth,delete e._prevHeight})}function a(t,n){var e=t.select("g."+n);return e.empty()&&(e=t.append("g").attr("class",n)),e}var u=t("./lodash"),c=t("./dagre").layout;n.exports=r;var s={paddingLeft:10,paddingRight:10,paddingTop:10,paddingBottom:10,rx:0,ry:0,shape:"rect"},f={arrowhead:"normal",lineInterpolate:"linear"}},{"./arrows":2,"./create-clusters":3,"./create-edge-labels":4,"./create-edge-paths":5,"./create-nodes":6,"./dagre":8,"./lodash":21,"./position-clusters":22,"./position-edge-labels":23,"./position-nodes":24,"./shapes":26}],26:[function(t,n,e){"use strict";function r(t,n,e){var r=t.insert("rect",":first-child").attr("rx",e.rx).attr("ry",e.ry).attr("x",-n.width/2).attr("y",-n.height/2).attr("width",n.width).attr("height",n.height);return e.intersect=function(t){return u(e,t)},r}function i(t,n,e){var r=n.width/2,i=n.height/2,o=t.insert("ellipse",":first-child").attr("x",-n.width/2).attr("y",-n.height/2).attr("rx",r).attr("ry",i);return e.intersect=function(t){return c(e,r,i,t)},o}function o(t,n,e){var r=Math.max(n.width,n.height)/2,i=t.insert("circle",":first-child").attr("x",-n.width/2).attr("y",-n.height/2).attr("r",r);return e.intersect=function(t){return s(e,r,t)},i}function a(t,n,e){var r=n.width*Math.SQRT2/2,i=n.height*Math.SQRT2/2,o=[{x:0,y:-i},{x:-r,y:0},{x:0,y:i},{x:r,y:0}],a=t.insert("polygon",":first-child").attr("points",o.map(function(t){return t.x+","+t.y}).join(" "));return e.intersect=function(t){return f(e,o,t)},a}var u=t("./intersect/intersect-rect"),c=t("./intersect/intersect-ellipse"),s=t("./intersect/intersect-circle"),f=t("./intersect/intersect-polygon");n.exports={rect:r,ellipse:i,circle:o,diamond:a}},{"./intersect/intersect-circle":11,"./intersect/intersect-ellipse":12,"./intersect/intersect-polygon":15,"./intersect/intersect-rect":16}],27:[function(t,n,e){function r(t,n){return!!t.children(n).length}function i(t){return o(t.v)+":"+o(t.w)+":"+o(t.name)}function o(t){return t?String(t).replace(f,"\\:"):""}function a(t,n){n&&t.attr("style",n)}function u(t,n,e){n&&t.attr("class",n).attr("class",e+" "+t.attr("class"))}function c(t,n){var e=n.graph();if(s.isPlainObject(e)){var r=e.transition;if(s.isFunction(r))return r(t)}return t}var s=t("./lodash");n.exports={isSubgraph:r,edgeToId:i,applyStyle:a,applyClass:u,applyTransition:c};var f=/:/g},{"./lodash":21}],28:[function(t,n,e){n.exports="0.4.17"},{}],29:[function(t,n,e){n.exports={graphlib:t("./lib/graphlib"),layout:t("./lib/layout"),debug:t("./lib/debug"),util:{time:t("./lib/util").time,notime:t("./lib/util").notime},version:t("./lib/version")}},{"./lib/debug":34,"./lib/graphlib":35,"./lib/layout":37,"./lib/util":57,"./lib/version":58}],30:[function(t,n,e){"use strict";function r(t){function n(t){return function(n){return t.edge(n).weight}}var e="greedy"===t.graph().acyclicer?u(t,n(t)):i(t);a.each(e,function(n){var e=t.edge(n);t.removeEdge(n),e.forwardName=n.name,e.reversed=!0,t.setEdge(n.w,n.v,e,a.uniqueId("rev"))})}function i(t){function n(o){a.has(i,o)||(i[o]=!0,r[o]=!0,a.each(t.outEdges(o),function(t){a.has(r,t.w)?e.push(t):n(t.w)}),delete r[o])}var e=[],r={},i={};return a.each(t.nodes(),n),e}function o(t){a.each(t.edges(),function(n){var e=t.edge(n);if(e.reversed){t.removeEdge(n);var r=e.forwardName;delete e.reversed,delete e.forwardName,t.setEdge(n.w,n.v,e,r)}})}var a=t("./lodash"),u=t("./greedy-fas");n.exports={run:r,undo:o}},{"./greedy-fas":36,"./lodash":38}],31:[function(t,n,e){function r(t){function n(e){var r=t.children(e),a=t.node(e);if(r.length&&o.each(r,n),o.has(a,"minRank")){a.borderLeft=[],a.borderRight=[];for(var u=a.minRank,c=a.maxRank+1;c>u;++u)i(t,"borderLeft","_bl",e,a,u),i(t,"borderRight","_br",e,a,u)}}o.each(t.children(),n)}function i(t,n,e,r,i,o){var u={width:0,height:0,rank:o,borderType:n},c=i[n][o-1],s=a.addDummyNode(t,"border",u,e);i[n][o]=s,t.setParent(s,r),c&&t.setEdge(c,s,{weight:1})}var o=t("./lodash"),a=t("./util");n.exports=r},{"./lodash":38,"./util":57}],32:[function(t,n,e){"use strict";function r(t){var n=t.graph().rankdir.toLowerCase();("lr"===n||"rl"===n)&&o(t)}function i(t){var n=t.graph().rankdir.toLowerCase();("bt"===n||"rl"===n)&&u(t),("lr"===n||"rl"===n)&&(s(t),o(t))}function o(t){l.each(t.nodes(),function(n){a(t.node(n))}),l.each(t.edges(),function(n){a(t.edge(n))})}function a(t){var n=t.width;t.width=t.height,t.height=n}function u(t){l.each(t.nodes(),function(n){c(t.node(n))}),l.each(t.edges(),function(n){var e=t.edge(n);l.each(e.points,c),l.has(e,"y")&&c(e)})}function c(t){t.y=-t.y}function s(t){l.each(t.nodes(),function(n){f(t.node(n))}),l.each(t.edges(),function(n){var e=t.edge(n);l.each(e.points,f),l.has(e,"x")&&f(e)})}function f(t){var n=t.x;t.x=t.y,t.y=n}var l=t("./lodash");n.exports={adjust:r,undo:i}},{"./lodash":38}],33:[function(t,n,e){function r(){var t={};t._next=t._prev=t,this._sentinel=t}function i(t){t._prev._next=t._next,t._next._prev=t._prev,delete t._next,delete t._prev}function o(t,n){return"_next"!==t&&"_prev"!==t?n:void 0}n.exports=r,r.prototype.dequeue=function(){var t=this._sentinel,n=t._prev;return n!==t?(i(n),n):void 0},r.prototype.enqueue=function(t){var n=this._sentinel;t._prev&&t._next&&i(t),t._next=n._next,n._next._prev=t,n._next=t,t._prev=n},r.prototype.toString=function(){for(var t=[],n=this._sentinel,e=n._prev;e!==n;)t.push(JSON.stringify(e,o)),e=e._prev;return"["+t.join(", ")+"]"}},{}],34:[function(t,n,e){function r(t){var n=o.buildLayerMatrix(t),e=new a({compound:!0,multigraph:!0}).setGraph({});return i.each(t.nodes(),function(n){e.setNode(n,{label:n}),e.setParent(n,"layer"+t.node(n).rank)}),i.each(t.edges(),function(t){e.setEdge(t.v,t.w,{},t.name)}),i.each(n,function(t,n){var r="layer"+n;e.setNode(r,{rank:"same"}),i.reduce(t,function(t,n){return e.setEdge(t,n,{style:"invis"}),n})}),e}var i=t("./lodash"),o=t("./util"),a=t("./graphlib").Graph;n.exports={debugOrdering:r}},{"./graphlib":35,"./lodash":38,"./util":57}],35:[function(t,n,e){arguments[4][9][0].apply(e,arguments)},{dup:9,graphlib:59}],36:[function(t,n,e){function r(t,n){if(t.nodeCount()<=1)return[];var e=a(t,n||l),r=i(e.graph,e.buckets,e.zeroIdx);return c.flatten(c.map(r,function(n){return t.outEdges(n.v,n.w)}),!0)}function i(t,n,e){for(var r,i=[],a=n[n.length-1],u=n[0];t.nodeCount();){for(;r=u.dequeue();)o(t,n,e,r);for(;r=a.dequeue();)o(t,n,e,r);if(t.nodeCount())for(var c=n.length-2;c>0;--c)if(r=n[c].dequeue()){i=i.concat(o(t,n,e,r,!0));break}}return i}function o(t,n,e,r,i){var o=i?[]:void 0;return c.each(t.inEdges(r.v),function(r){var a=t.edge(r),c=t.node(r.v);i&&o.push({v:r.v,w:r.w}),c.out-=a,u(n,e,c)}),c.each(t.outEdges(r.v),function(r){var i=t.edge(r),o=r.w,a=t.node(o);a["in"]-=i,u(n,e,a)}),t.removeNode(r.v),o}function a(t,n){var e=new s,r=0,i=0;c.each(t.nodes(),function(t){e.setNode(t,{v:t,"in":0,out:0})}),c.each(t.edges(),function(t){var o=e.edge(t.v,t.w)||0,a=n(t),u=o+a;e.setEdge(t.v,t.w,u),i=Math.max(i,e.node(t.v).out+=a),r=Math.max(r,e.node(t.w)["in"]+=a)});var o=c.range(i+r+3).map(function(){return new f}),a=r+1;return c.each(e.nodes(),function(t){u(o,a,e.node(t))}),{graph:e,buckets:o,zeroIdx:a}}function u(t,n,e){e.out?e["in"]?t[e.out-e["in"]+n].enqueue(e):t[t.length-1].enqueue(e):t[0].enqueue(e)}var c=t("./lodash"),s=t("./graphlib").Graph,f=t("./data/list");n.exports=r;var l=c.constant(1)},{"./data/list":33,"./graphlib":35,"./lodash":38}],37:[function(t,n,e){"use strict";function r(t,n){var e=n&&n.debugTiming?O.time:O.notime;e("layout",function(){var n=e("  buildLayoutGraph",function(){return a(t)});e("  runLayout",function(){i(n,e)}),e("  updateInputGraph",function(){o(t,n)})})}function i(t,n){n("    makeSpaceForEdgeLabels",function(){u(t)}),n("    removeSelfEdges",function(){v(t)}),n("    acyclic",function(){x.run(t)}),n("    nestingGraph.run",function(){j.run(t)}),n("    rank",function(){E(O.asNonCompoundGraph(t))}),n("    injectEdgeLabelProxies",function(){c(t)}),n("    removeEmptyRanks",function(){C(t)}),n("    nestingGraph.cleanup",function(){j.cleanup(t)}),n("    normalizeRanks",function(){I(t)}),n("    assignRankMinMax",function(){s(t)}),n("    removeEdgeLabelProxies",function(){f(t)}),n("    normalize.run",function(){k.run(t)}),n("    parentDummyChains",function(){N(t)}),n("    addBorderSegments",function(){R(t)}),n("    order",function(){L(t)}),n("    insertSelfEdges",function(){y(t)}),n("    adjustCoordinateSystem",function(){T.adjust(t)}),n("    position",function(){A(t)}),n("    positionSelfEdges",function(){_(t)}),n("    removeBorderNodes",function(){g(t)}),n("    normalize.undo",function(){k.undo(t)}),n("    fixupEdgeLabelCoords",function(){d(t)}),n("    undoCoordinateSystem",function(){T.undo(t)}),n("    translateGraph",function(){l(t)}),n("    assignNodeIntersects",function(){h(t)}),n("    reversePoints",function(){p(t)}),n("    acyclic.undo",function(){x.undo(t)})}function o(t,n){b.each(t.nodes(),function(e){var r=t.node(e),i=n.node(e);r&&(r.x=i.x,r.y=i.y,n.children(e).length&&(r.width=i.width,r.height=i.height))}),b.each(t.edges(),function(e){var r=t.edge(e),i=n.edge(e);r.points=i.points,b.has(i,"x")&&(r.x=i.x,r.y=i.y)}),t.graph().width=n.graph().width,t.graph().height=n.graph().height}function a(t){var n=new S({multigraph:!0,compound:!0}),e=w(t.graph());return n.setGraph(b.merge({},B,m(e,M),b.pick(e,U))),b.each(t.nodes(),function(e){var r=w(t.node(e));n.setNode(e,b.defaults(m(r,P),F)),n.setParent(e,t.parent(e))}),b.each(t.edges(),function(e){var r=w(t.edge(e));n.setEdge(e,b.merge({},W,m(r,D),b.pick(r,z)))}),n}function u(t){var n=t.graph();n.ranksep/=2,b.each(t.edges(),function(e){var r=t.edge(e);r.minlen*=2,"c"!==r.labelpos.toLowerCase()&&("TB"===n.rankdir||"BT"===n.rankdir?r.width+=r.labeloffset:r.height+=r.labeloffset)})}function c(t){b.each(t.edges(),function(n){var e=t.edge(n);if(e.width&&e.height){var r=t.node(n.v),i=t.node(n.w),o={rank:(i.rank-r.rank)/2+r.rank,e:n};O.addDummyNode(t,"edge-proxy",o,"_ep")}})}function s(t){var n=0;b.each(t.nodes(),function(e){var r=t.node(e);r.borderTop&&(r.minRank=t.node(r.borderTop).rank,r.maxRank=t.node(r.borderBottom).rank,n=b.max(n,r.maxRank))}),t.graph().maxRank=n}function f(t){b.each(t.nodes(),function(n){var e=t.node(n);"edge-proxy"===e.dummy&&(t.edge(e.e).labelRank=e.rank,t.removeNode(n))})}function l(t){function n(t){var n=t.x,a=t.y,u=t.width,c=t.height;e=Math.min(e,n-u/2),r=Math.max(r,n+u/2),i=Math.min(i,a-c/2),o=Math.max(o,a+c/2)}var e=Number.POSITIVE_INFINITY,r=0,i=Number.POSITIVE_INFINITY,o=0,a=t.graph(),u=a.marginx||0,c=a.marginy||0;b.each(t.nodes(),function(e){n(t.node(e))}),b.each(t.edges(),function(e){var r=t.edge(e);b.has(r,"x")&&n(r)}),e-=u,i-=c,b.each(t.nodes(),function(n){var r=t.node(n);r.x-=e,r.y-=i}),b.each(t.edges(),function(n){var r=t.edge(n);b.each(r.points,function(t){t.x-=e,t.y-=i}),b.has(r,"x")&&(r.x-=e),b.has(r,"y")&&(r.y-=i)}),a.width=r-e+u,a.height=o-i+c}function h(t){b.each(t.edges(),function(n){var e,r,i=t.edge(n),o=t.node(n.v),a=t.node(n.w);i.points?(e=i.points[0],r=i.points[i.points.length-1]):(i.points=[],e=a,r=o),i.points.unshift(O.intersectRect(o,e)),i.points.push(O.intersectRect(a,r))})}function d(t){b.each(t.edges(),function(n){var e=t.edge(n);if(b.has(e,"x"))switch(("l"===e.labelpos||"r"===e.labelpos)&&(e.width-=e.labeloffset),e.labelpos){case"l":e.x-=e.width/2+e.labeloffset;break;case"r":e.x+=e.width/2+e.labeloffset}})}function p(t){b.each(t.edges(),function(n){var e=t.edge(n);e.reversed&&e.points.reverse()})}function g(t){b.each(t.nodes(),function(n){if(t.children(n).length){var e=t.node(n),r=t.node(e.borderTop),i=t.node(e.borderBottom),o=t.node(b.last(e.borderLeft)),a=t.node(b.last(e.borderRight));e.width=Math.abs(a.x-o.x),e.height=Math.abs(i.y-r.y),e.x=o.x+e.width/2,e.y=r.y+e.height/2}}),b.each(t.nodes(),function(n){"border"===t.node(n).dummy&&t.removeNode(n)})}function v(t){b.each(t.edges(),function(n){if(n.v===n.w){var e=t.node(n.v);e.selfEdges||(e.selfEdges=[]),e.selfEdges.push({e:n,label:t.edge(n)}),t.removeEdge(n)}})}function y(t){var n=O.buildLayerMatrix(t);b.each(n,function(n){var e=0;b.each(n,function(n,r){var i=t.node(n);i.order=r+e,b.each(i.selfEdges,function(n){O.addDummyNode(t,"selfedge",{width:n.label.width,height:n.label.height,rank:i.rank,order:r+ ++e,e:n.e,label:n.label},"_se")}),delete i.selfEdges})})}function _(t){b.each(t.nodes(),function(n){var e=t.node(n);if("selfedge"===e.dummy){var r=t.node(e.e.v),i=r.x+r.width/2,o=r.y,a=e.x-i,u=r.height/2;t.setEdge(e.e,e.label),t.removeNode(n),e.label.points=[{x:i+2*a/3,y:o-u},{x:i+5*a/6,y:o-u},{x:i+a,y:o},{x:i+5*a/6,y:o+u},{x:i+2*a/3,y:o+u}],e.label.x=e.x,e.label.y=e.y}})}function m(t,n){return b.mapValues(b.pick(t,n),Number)}function w(t){var n={};return b.each(t,function(t,e){n[e.toLowerCase()]=t}),n}var b=t("./lodash"),x=t("./acyclic"),k=t("./normalize"),E=t("./rank"),I=t("./util").normalizeRanks,N=t("./parent-dummy-chains"),C=t("./util").removeEmptyRanks,j=t("./nesting-graph"),R=t("./add-border-segments"),T=t("./coordinate-system"),L=t("./order"),A=t("./position"),O=t("./util"),S=t("./graphlib").Graph;n.exports=r;var M=["nodesep","edgesep","ranksep","marginx","marginy"],B={ranksep:50,edgesep:20,nodesep:50,rankdir:"tb"},U=["acyclicer","ranker","rankdir","align"],P=["width","height"],F={width:0,height:0},D=["minlen","weight","width","height","labeloffset"],W={minlen:1,weight:1,width:0,height:0,labeloffset:10,labelpos:"r"},z=["labelpos"]},{"./acyclic":30,"./add-border-segments":31,"./coordinate-system":32,"./graphlib":35,"./lodash":38,"./nesting-graph":39,"./normalize":40,"./order":45,"./parent-dummy-chains":50,"./position":52,"./rank":54,"./util":57}],38:[function(t,n,e){arguments[4][21][0].apply(e,arguments)},{dup:21,lodash:79}],39:[function(t,n,e){function r(t){var n=s.addDummyNode(t,"root",{},"_root"),e=o(t),r=c.max(e)-1,u=2*r+1;t.graph().nestingRoot=n,c.each(t.edges(),function(n){t.edge(n).minlen*=u});var f=a(t)+1;c.each(t.children(),function(o){i(t,n,u,f,r,e,o)}),t.graph().nodeRankFactor=u}function i(t,n,e,r,o,a,u){var f=t.children(u);if(!f.length)return void(u!==n&&t.setEdge(n,u,{weight:0,minlen:e}));var l=s.addBorderNode(t,"_bt"),h=s.addBorderNode(t,"_bb"),d=t.node(u);t.setParent(l,u),d.borderTop=l,t.setParent(h,u),d.borderBottom=h,c.each(f,function(c){i(t,n,e,r,o,a,c);var s=t.node(c),f=s.borderTop?s.borderTop:c,d=s.borderBottom?s.borderBottom:c,p=s.borderTop?r:2*r,g=f!==d?1:o-a[u]+1;t.setEdge(l,f,{weight:p,minlen:g,nestingEdge:!0}),t.setEdge(d,h,{weight:p,minlen:g,nestingEdge:!0})}),t.parent(u)||t.setEdge(n,l,{weight:0,minlen:o+a[u]})}function o(t){function n(r,i){var o=t.children(r);o&&o.length&&c.each(o,function(t){n(t,i+1)}),e[r]=i}var e={};return c.each(t.children(),function(t){n(t,1)}),e}function a(t){return c.reduce(t.edges(),function(n,e){return n+t.edge(e).weight},0)}function u(t){var n=t.graph();t.removeNode(n.nestingRoot),delete n.nestingRoot,c.each(t.edges(),function(n){var e=t.edge(n);e.nestingEdge&&t.removeEdge(n)})}var c=t("./lodash"),s=t("./util");n.exports={run:r,cleanup:u}},{"./lodash":38,"./util":57}],40:[function(t,n,e){"use strict";function r(t){t.graph().dummyChains=[],a.each(t.edges(),function(n){i(t,n)})}function i(t,n){var e=n.v,r=t.node(e).rank,i=n.w,o=t.node(i).rank,a=n.name,c=t.edge(n),s=c.labelRank;if(o!==r+1){t.removeEdge(n);var f,l,h;for(h=0,++r;o>r;++h,++r)c.points=[],l={width:0,height:0,edgeLabel:c,edgeObj:n,rank:r},f=u.addDummyNode(t,"edge",l,"_d"),r===s&&(l.width=c.width,l.height=c.height,l.dummy="edge-label",l.labelpos=c.labelpos),t.setEdge(e,f,{weight:c.weight},a),0===h&&t.graph().dummyChains.push(f),e=f;t.setEdge(e,i,{weight:c.weight},a)}}function o(t){a.each(t.graph().dummyChains,function(n){var e,r=t.node(n),i=r.edgeLabel;for(t.setEdge(r.edgeObj,i);r.dummy;)e=t.successors(n)[0],t.removeNode(n),i.points.push({x:r.x,y:r.y}),"edge-label"===r.dummy&&(i.x=r.x,i.y=r.y,i.width=r.width,i.height=r.height),n=e,r=t.node(n)})}var a=t("./lodash"),u=t("./util");n.exports={run:r,undo:o}},{"./lodash":38,"./util":57}],41:[function(t,n,e){function r(t,n,e){var r,o={};i.each(e,function(e){for(var i,a,u=t.parent(e);u;){if(i=t.parent(u),i?(a=o[i],o[i]=u):(a=r,r=u),a&&a!==u)return void n.setEdge(a,u);u=i}})}var i=t("../lodash");n.exports=r},{"../lodash":38}],42:[function(t,n,e){function r(t,n){return i.map(n,function(n){var e=t.inEdges(n);if(e.length){var r=i.reduce(e,function(n,e){var r=t.edge(e),i=t.node(e.v);return{sum:n.sum+r.weight*i.order,weight:n.weight+r.weight}},{sum:0,weight:0});return{v:n,barycenter:r.sum/r.weight,weight:r.weight}}return{v:n}})}var i=t("../lodash");n.exports=r},{"../lodash":38}],43:[function(t,n,e){function r(t,n,e){var r=i(t),u=new a({compound:!0}).setGraph({root:r}).setDefaultNodeLabel(function(n){return t.node(n)});return o.each(t.nodes(),function(i){var a=t.node(i),c=t.parent(i);(a.rank===n||a.minRank<=n&&n<=a.maxRank)&&(u.setNode(i),u.setParent(i,c||r),o.each(t[e](i),function(n){var e=n.v===i?n.w:n.v,r=u.edge(e,i),a=o.isUndefined(r)?0:r.weight;u.setEdge(e,i,{weight:t.edge(n).weight+a})}),o.has(a,"minRank")&&u.setNode(i,{borderLeft:a.borderLeft[n],borderRight:a.borderRight[n]}))}),u}function i(t){for(var n;t.hasNode(n=o.uniqueId("_root")););return n}var o=t("../lodash"),a=t("../graphlib").Graph;n.exports=r},{"../graphlib":35,"../lodash":38}],44:[function(t,n,e){"use strict";function r(t,n){for(var e=0,r=1;r<n.length;++r)e+=i(t,n[r-1],n[r]);return e}function i(t,n,e){for(var r=o.zipObject(e,o.map(e,function(t,n){return n})),i=o.flatten(o.map(n,function(n){return o.chain(t.outEdges(n)).map(function(n){return{pos:r[n.w],weight:t.edge(n).weight}}).sortBy("pos").value()}),!0),a=1;a<e.length;)a<<=1;var u=2*a-1;a-=1;var c=o.map(new Array(u),function(){return 0}),s=0;return o.each(i.forEach(function(t){var n=t.pos+a;c[n]+=t.weight;for(var e=0;n>0;)n%2&&(e+=c[n+1]),n=n-1>>1,c[n]+=t.weight;s+=t.weight*e})),s}var o=t("../lodash");n.exports=r},{"../lodash":38}],45:[function(t,n,e){"use strict";function r(t){var n=p.maxRank(t),e=i(t,u.range(1,n+1),"inEdges"),r=i(t,u.range(n-1,-1,-1),"outEdges"),f=c(t);a(t,f);for(var l,h=Number.POSITIVE_INFINITY,d=0,g=0;4>g;++d,++g){o(d%2?e:r,d%4>=2),f=p.buildLayerMatrix(t);var v=s(t,f);h>v&&(g=0,l=u.cloneDeep(f),h=v)}a(t,l)}function i(t,n,e){return u.map(n,function(n){return l(t,n,e)})}function o(t,n){var e=new d;u.each(t,function(t){var r=t.graph().root,i=f(t,r,e,n);u.each(i.vs,function(n,e){t.node(n).order=e}),h(t,e,i.vs)})}function a(t,n){u.each(n,function(n){u.each(n,function(n,e){t.node(n).order=e})})}var u=t("../lodash"),c=t("./init-order"),s=t("./cross-count"),f=t("./sort-subgraph"),l=t("./build-layer-graph"),h=t("./add-subgraph-constraints"),d=t("../graphlib").Graph,p=t("../util");n.exports=r},{"../graphlib":35,"../lodash":38,"../util":57,"./add-subgraph-constraints":41,"./build-layer-graph":43,"./cross-count":44,"./init-order":46,"./sort-subgraph":48}],46:[function(t,n,e){"use strict";

        function r(t){function n(r){if(!i.has(e,r)){e[r]=!0;var o=t.node(r);a[o.rank].push(r),i.each(t.successors(r),n)}}var e={},r=i.filter(t.nodes(),function(n){return!t.children(n).length}),o=i.max(i.map(r,function(n){return t.node(n).rank})),a=i.map(i.range(o+1),function(){return[]}),u=i.sortBy(r,function(n){return t.node(n).rank});return i.each(u,n),a}var i=t("../lodash");n.exports=r},{"../lodash":38}],47:[function(t,n,e){"use strict";function r(t,n){var e={};a.each(t,function(t,n){var r=e[t.v]={indegree:0,"in":[],out:[],vs:[t.v],i:n};a.isUndefined(t.barycenter)||(r.barycenter=t.barycenter,r.weight=t.weight)}),a.each(n.edges(),function(t){var n=e[t.v],r=e[t.w];a.isUndefined(n)||a.isUndefined(r)||(r.indegree++,n.out.push(e[t.w]))});var r=a.filter(e,function(t){return!t.indegree});return i(r)}function i(t){function n(t){return function(n){n.merged||(a.isUndefined(n.barycenter)||a.isUndefined(t.barycenter)||n.barycenter>=t.barycenter)&&o(t,n)}}function e(n){return function(e){e["in"].push(n),0===--e.indegree&&t.push(e)}}for(var r=[];t.length;){var i=t.pop();r.push(i),a.each(i["in"].reverse(),n(i)),a.each(i.out,e(i))}return a.chain(r).filter(function(t){return!t.merged}).map(function(t){return a.pick(t,["vs","i","barycenter","weight"])}).value()}function o(t,n){var e=0,r=0;t.weight&&(e+=t.barycenter*t.weight,r+=t.weight),n.weight&&(e+=n.barycenter*n.weight,r+=n.weight),t.vs=n.vs.concat(t.vs),t.barycenter=e/r,t.weight=r,t.i=Math.min(n.i,t.i),n.merged=!0}var a=t("../lodash");n.exports=r},{"../lodash":38}],48:[function(t,n,e){function r(t,n,e,f){var l=t.children(n),h=t.node(n),d=h?h.borderLeft:void 0,p=h?h.borderRight:void 0,g={};d&&(l=a.filter(l,function(t){return t!==d&&t!==p}));var v=u(t,l);a.each(v,function(n){if(t.children(n.v).length){var i=r(t,n.v,e,f);g[n.v]=i,a.has(i,"barycenter")&&o(n,i)}});var y=c(v,e);i(y,g);var _=s(y,f);if(d&&(_.vs=a.flatten([d,_.vs,p],!0),t.predecessors(d).length)){var m=t.node(t.predecessors(d)[0]),w=t.node(t.predecessors(p)[0]);a.has(_,"barycenter")||(_.barycenter=0,_.weight=0),_.barycenter=(_.barycenter*_.weight+m.order+w.order)/(_.weight+2),_.weight+=2}return _}function i(t,n){a.each(t,function(t){t.vs=a.flatten(t.vs.map(function(t){return n[t]?n[t].vs:t}),!0)})}function o(t,n){a.isUndefined(t.barycenter)?(t.barycenter=n.barycenter,t.weight=n.weight):(t.barycenter=(t.barycenter*t.weight+n.barycenter*n.weight)/(t.weight+n.weight),t.weight+=n.weight)}var a=t("../lodash"),u=t("./barycenter"),c=t("./resolve-conflicts"),s=t("./sort");n.exports=r},{"../lodash":38,"./barycenter":42,"./resolve-conflicts":47,"./sort":49}],49:[function(t,n,e){function r(t,n){var e=u.partition(t,function(t){return a.has(t,"barycenter")}),r=e.lhs,c=a.sortBy(e.rhs,function(t){return-t.i}),s=[],f=0,l=0,h=0;r.sort(o(!!n)),h=i(s,c,h),a.each(r,function(t){h+=t.vs.length,s.push(t.vs),f+=t.barycenter*t.weight,l+=t.weight,h=i(s,c,h)});var d={vs:a.flatten(s,!0)};return l&&(d.barycenter=f/l,d.weight=l),d}function i(t,n,e){for(var r;n.length&&(r=a.last(n)).i<=e;)n.pop(),t.push(r.vs),e++;return e}function o(t){return function(n,e){return n.barycenter<e.barycenter?-1:n.barycenter>e.barycenter?1:t?e.i-n.i:n.i-e.i}}var a=t("../lodash"),u=t("../util");n.exports=r},{"../lodash":38,"../util":57}],50:[function(t,n,e){function r(t){var n=o(t);a.each(t.graph().dummyChains,function(e){for(var r=t.node(e),o=r.edgeObj,a=i(t,n,o.v,o.w),u=a.path,c=a.lca,s=0,f=u[s],l=!0;e!==o.w;){if(r=t.node(e),l){for(;(f=u[s])!==c&&t.node(f).maxRank<r.rank;)s++;f===c&&(l=!1)}if(!l){for(;s<u.length-1&&t.node(f=u[s+1]).minRank<=r.rank;)s++;f=u[s]}t.setParent(e,f),e=t.successors(e)[0]}})}function i(t,n,e,r){var i,o,a=[],u=[],c=Math.min(n[e].low,n[r].low),s=Math.max(n[e].lim,n[r].lim);i=e;do i=t.parent(i),a.push(i);while(i&&(n[i].low>c||s>n[i].lim));for(o=i,i=r;(i=t.parent(i))!==o;)u.push(i);return{path:a.concat(u.reverse()),lca:o}}function o(t){function n(i){var o=r;a.each(t.children(i),n),e[i]={low:o,lim:r++}}var e={},r=0;return a.each(t.children(),n),e}var a=t("./lodash");n.exports=r},{"./lodash":38}],51:[function(t,n,e){"use strict";function r(t,n){function e(n,e){var i=0,u=0,c=n.length,s=y.last(e);return y.each(e,function(n,f){var l=o(t,n),h=l?t.node(l).order:c;(l||n===s)&&(y.each(e.slice(u,f+1),function(n){y.each(t.predecessors(n),function(e){var o=t.node(e),u=o.order;!(i>u||u>h)||o.dummy&&t.node(n).dummy||a(r,e,n)})}),u=f+1,i=h)}),e}var r={};return y.reduce(n,e),r}function i(t,n){function e(n,e,r,o,u){var c;y.each(y.range(e,r),function(e){c=n[e],t.node(c).dummy&&y.each(t.predecessors(c),function(n){var e=t.node(n);e.dummy&&(e.order<o||e.order>u)&&a(i,n,c)})})}function r(n,r){var i,o=-1,a=0;return y.each(r,function(u,c){if("border"===t.node(u).dummy){var s=t.predecessors(u);s.length&&(i=t.node(s[0]).order,e(r,a,c,o,i),a=c,o=i)}e(r,a,r.length,i,n.length)}),r}var i={};return y.reduce(n,r),i}function o(t,n){return t.node(n).dummy?y.find(t.predecessors(n),function(n){return t.node(n).dummy}):void 0}function a(t,n,e){if(n>e){var r=n;n=e,e=r}var i=t[n];i||(t[n]=i={}),i[e]=!0}function u(t,n,e){if(n>e){var r=n;n=e,e=r}return y.has(t[n],e)}function c(t,n,e,r){var i={},o={},a={};return y.each(n,function(t){y.each(t,function(t,n){i[t]=t,o[t]=t,a[t]=n})}),y.each(n,function(t){var n=-1;y.each(t,function(t){var c=r(t);if(c.length){c=y.sortBy(c,function(t){return a[t]});for(var s=(c.length-1)/2,f=Math.floor(s),l=Math.ceil(s);l>=f;++f){var h=c[f];o[t]===t&&n<a[h]&&!u(e,t,h)&&(o[h]=t,o[t]=i[t]=i[h],n=a[h])}}})}),{root:i,align:o}}function s(t,n,e,r,i){function o(t){y.has(s,t)||(s[t]=!0,u[t]=y.reduce(c.inEdges(t),function(t,n){return o(n.v),Math.max(t,u[n.v]+c.edge(n))},0))}function a(n){if(2!==s[n]){s[n]++;var e=t.node(n),r=y.reduce(c.outEdges(n),function(t,n){return a(n.w),Math.min(t,u[n.w]-c.edge(n))},Number.POSITIVE_INFINITY);r!==Number.POSITIVE_INFINITY&&e.borderType!==l&&(u[n]=Math.max(u[n],r))}}var u={},c=f(t,n,e,i),s={};y.each(c.nodes(),o);var l=i?"borderLeft":"borderRight";return y.each(c.nodes(),a),y.each(r,function(t){u[t]=u[e[t]]}),u}function f(t,n,e,r){var i=new _,o=t.graph(),a=g(o.nodesep,o.edgesep,r);return y.each(n,function(n){var r;y.each(n,function(n){var o=e[n];if(i.setNode(o),r){var u=e[r],c=i.edge(u,o);i.setEdge(u,o,Math.max(a(t,n,r),c||0))}r=n})}),i}function l(t,n){return y.min(n,function(n){var e=y.min(n,function(n,e){return n-v(t,e)/2}),r=y.max(n,function(n,e){return n+v(t,e)/2});return r-e})}function h(t,n){var e=y.min(n),r=y.max(n);y.each(["u","d"],function(i){y.each(["l","r"],function(o){var a,u=i+o,c=t[u];c!==n&&(a="l"===o?e-y.min(c):r-y.max(c),a&&(t[u]=y.mapValues(c,function(t){return t+a})))})})}function d(t,n){return y.mapValues(t.ul,function(e,r){if(n)return t[n.toLowerCase()][r];var i=y.sortBy(y.pluck(t,r));return(i[1]+i[2])/2})}function p(t){var n,e=m.buildLayerMatrix(t),o=y.merge(r(t,e),i(t,e)),a={};y.each(["u","d"],function(r){n="u"===r?e:y.values(e).reverse(),y.each(["l","r"],function(e){"r"===e&&(n=y.map(n,function(t){return y.values(t).reverse()}));var i=y.bind("u"===r?t.predecessors:t.successors,t),u=c(t,n,o,i),f=s(t,n,u.root,u.align,"r"===e);"r"===e&&(f=y.mapValues(f,function(t){return-t})),a[r+e]=f})});var u=l(t,a);return h(a,u),d(a,t.graph().align)}function g(t,n,e){return function(r,i,o){var a,u=r.node(i),c=r.node(o),s=0;if(s+=u.width/2,y.has(u,"labelpos"))switch(u.labelpos.toLowerCase()){case"l":a=-u.width/2;break;case"r":a=u.width/2}if(a&&(s+=e?a:-a),a=0,s+=(u.dummy?n:t)/2,s+=(c.dummy?n:t)/2,s+=c.width/2,y.has(c,"labelpos"))switch(c.labelpos.toLowerCase()){case"l":a=c.width/2;break;case"r":a=-c.width/2}return a&&(s+=e?a:-a),a=0,s}}function v(t,n){return t.node(n).width}var y=t("../lodash"),_=t("../graphlib").Graph,m=t("../util");n.exports={positionX:p,findType1Conflicts:r,findType2Conflicts:i,addConflict:a,hasConflict:u,verticalAlignment:c,horizontalCompaction:s,alignCoordinates:h,findSmallestWidthAlignment:l,balance:d}},{"../graphlib":35,"../lodash":38,"../util":57}],52:[function(t,n,e){"use strict";function r(t){t=a.asNonCompoundGraph(t),i(t),o.each(u(t),function(n,e){t.node(e).x=n})}function i(t){var n=a.buildLayerMatrix(t),e=t.graph().ranksep,r=0;o.each(n,function(n){var i=o.max(o.map(n,function(n){return t.node(n).height}));o.each(n,function(n){t.node(n).y=r+i/2}),r+=i+e})}var o=t("../lodash"),a=t("../util"),u=t("./bk").positionX;n.exports=r},{"../lodash":38,"../util":57,"./bk":51}],53:[function(t,n,e){"use strict";function r(t){var n=new c({directed:!1}),e=t.nodes()[0],r=t.nodeCount();n.setNode(e,{});for(var u,f;i(n,t)<r;)u=o(n,t),f=n.hasNode(u.v)?s(t,u):-s(t,u),a(n,t,f);return n}function i(t,n){function e(r){u.each(n.nodeEdges(r),function(i){var o=i.v,a=r===o?i.w:o;t.hasNode(a)||s(n,i)||(t.setNode(a,{}),t.setEdge(r,a,{}),e(a))})}return u.each(t.nodes(),e),t.nodeCount()}function o(t,n){return u.min(n.edges(),function(e){return t.hasNode(e.v)!==t.hasNode(e.w)?s(n,e):void 0})}function a(t,n,e){u.each(t.nodes(),function(t){n.node(t).rank+=e})}var u=t("../lodash"),c=t("../graphlib").Graph,s=t("./util").slack;n.exports=r},{"../graphlib":35,"../lodash":38,"./util":56}],54:[function(t,n,e){"use strict";function r(t){switch(t.graph().ranker){case"network-simplex":o(t);break;case"tight-tree":i(t);break;case"longest-path":f(t);break;default:o(t)}}function i(t){u(t),c(t)}function o(t){s(t)}var a=t("./util"),u=a.longestPath,c=t("./feasible-tree"),s=t("./network-simplex");n.exports=r;var f=u},{"./feasible-tree":53,"./network-simplex":55,"./util":56}],55:[function(t,n,e){"use strict";function r(t){t=b(t),_(t);var n=v(t);u(n),i(n,t);for(var e,r;e=s(n);)r=f(n,t,e),l(n,t,e,r)}function i(t,n){var e=w(t,t.nodes());e=e.slice(0,e.length-1),g.each(e,function(e){o(t,n,e)})}function o(t,n,e){var r=t.node(e),i=r.parent;t.edge(e,i).cutvalue=a(t,n,e)}function a(t,n,e){var r=t.node(e),i=r.parent,o=!0,a=n.edge(e,i),u=0;return a||(o=!1,a=n.edge(i,e)),u=a.weight,g.each(n.nodeEdges(e),function(r){var a=r.v===e,c=a?r.w:r.v;if(c!==i){var s=a===o,f=n.edge(r).weight;if(u+=s?f:-f,d(t,e,c)){var l=t.edge(e,c).cutvalue;u+=s?-l:l}}}),u}function u(t,n){arguments.length<2&&(n=t.nodes()[0]),c(t,{},1,n)}function c(t,n,e,r,i){var o=e,a=t.node(r);return n[r]=!0,g.each(t.neighbors(r),function(i){g.has(n,i)||(e=c(t,n,e,i,r))}),a.low=o,a.lim=e++,i?a.parent=i:delete a.parent,e}function s(t){return g.find(t.edges(),function(n){return t.edge(n).cutvalue<0})}function f(t,n,e){var r=e.v,i=e.w;n.hasEdge(r,i)||(r=e.w,i=e.v);var o=t.node(r),a=t.node(i),u=o,c=!1;o.lim>a.lim&&(u=a,c=!0);var s=g.filter(n.edges(),function(n){return c===p(t,t.node(n.v),u)&&c!==p(t,t.node(n.w),u)});return g.min(s,function(t){return y(n,t)})}function l(t,n,e,r){var o=e.v,a=e.w;t.removeEdge(o,a),t.setEdge(r.v,r.w,{}),u(t),i(t,n),h(t,n)}function h(t,n){var e=g.find(t.nodes(),function(t){return!n.node(t).parent}),r=m(t,e);r=r.slice(1),g.each(r,function(e){var r=t.node(e).parent,i=n.edge(e,r),o=!1;i||(i=n.edge(r,e),o=!0),n.node(e).rank=n.node(r).rank+(o?i.minlen:-i.minlen)})}function d(t,n,e){return t.hasEdge(n,e)}function p(t,n,e){return e.low<=n.lim&&n.lim<=e.lim}var g=t("../lodash"),v=t("./feasible-tree"),y=t("./util").slack,_=t("./util").longestPath,m=t("../graphlib").alg.preorder,w=t("../graphlib").alg.postorder,b=t("../util").simplify;n.exports=r,r.initLowLimValues=u,r.initCutValues=i,r.calcCutValue=a,r.leaveEdge=s,r.enterEdge=f,r.exchangeEdges=l},{"../graphlib":35,"../lodash":38,"../util":57,"./feasible-tree":53,"./util":56}],56:[function(t,n,e){"use strict";function r(t){function n(r){var i=t.node(r);if(o.has(e,r))return i.rank;e[r]=!0;var a=o.min(o.map(t.outEdges(r),function(e){return n(e.w)-t.edge(e).minlen}));return a===Number.POSITIVE_INFINITY&&(a=0),i.rank=a}var e={};o.each(t.sources(),n)}function i(t,n){return t.node(n.w).rank-t.node(n.v).rank-t.edge(n).minlen}var o=t("../lodash");n.exports={longestPath:r,slack:i}},{"../lodash":38}],57:[function(t,n,e){"use strict";function r(t,n,e,r){var i;do i=y.uniqueId(r);while(t.hasNode(i));return e.dummy=n,t.setNode(i,e),i}function i(t){var n=(new _).setGraph(t.graph());return y.each(t.nodes(),function(e){n.setNode(e,t.node(e))}),y.each(t.edges(),function(e){var r=n.edge(e.v,e.w)||{weight:0,minlen:1},i=t.edge(e);n.setEdge(e.v,e.w,{weight:r.weight+i.weight,minlen:Math.max(r.minlen,i.minlen)})}),n}function o(t){var n=new _({multigraph:t.isMultigraph()}).setGraph(t.graph());return y.each(t.nodes(),function(e){t.children(e).length||n.setNode(e,t.node(e))}),y.each(t.edges(),function(e){n.setEdge(e,t.edge(e))}),n}function a(t){var n=y.map(t.nodes(),function(n){var e={};return y.each(t.outEdges(n),function(n){e[n.w]=(e[n.w]||0)+t.edge(n).weight}),e});return y.zipObject(t.nodes(),n)}function u(t){var n=y.map(t.nodes(),function(n){var e={};return y.each(t.inEdges(n),function(n){e[n.v]=(e[n.v]||0)+t.edge(n).weight}),e});return y.zipObject(t.nodes(),n)}function c(t,n){var e=t.x,r=t.y,i=n.x-e,o=n.y-r,a=t.width/2,u=t.height/2;if(!i&&!o)throw new Error("Not possible to find intersection inside of the rectangle");var c,s;return Math.abs(o)*a>Math.abs(i)*u?(0>o&&(u=-u),c=u*i/o,s=u):(0>i&&(a=-a),c=a,s=a*o/i),{x:e+c,y:r+s}}function s(t){var n=y.map(y.range(d(t)+1),function(){return[]});return y.each(t.nodes(),function(e){var r=t.node(e),i=r.rank;y.isUndefined(i)||(n[i][r.order]=e)}),n}function f(t){var n=y.min(y.map(t.nodes(),function(n){return t.node(n).rank}));y.each(t.nodes(),function(e){var r=t.node(e);y.has(r,"rank")&&(r.rank-=n)})}function l(t){var n=y.min(y.map(t.nodes(),function(n){return t.node(n).rank})),e=[];y.each(t.nodes(),function(r){var i=t.node(r).rank-n;e[i]||(e[i]=[]),e[i].push(r)});var r=0,i=t.graph().nodeRankFactor;y.each(e,function(n,e){y.isUndefined(n)&&e%i!==0?--r:r&&y.each(n,function(n){t.node(n).rank+=r})})}function h(t,n,e,i){var o={width:0,height:0};return arguments.length>=4&&(o.rank=e,o.order=i),r(t,"border",o,n)}function d(t){return y.max(y.map(t.nodes(),function(n){var e=t.node(n).rank;return y.isUndefined(e)?void 0:e}))}function p(t,n){var e={lhs:[],rhs:[]};return y.each(t,function(t){n(t)?e.lhs.push(t):e.rhs.push(t)}),e}function g(t,n){var e=y.now();try{return n()}finally{console.log(t+" time: "+(y.now()-e)+"ms")}}function v(t,n){return n()}var y=t("./lodash"),_=t("./graphlib").Graph;n.exports={addDummyNode:r,simplify:i,asNonCompoundGraph:o,successorWeights:a,predecessorWeights:u,intersectRect:c,buildLayerMatrix:s,normalizeRanks:f,removeEmptyRanks:l,addBorderNode:h,maxRank:d,partition:p,time:g,notime:v}},{"./graphlib":35,"./lodash":38}],58:[function(t,n,e){n.exports="0.7.3"},{}],59:[function(t,n,e){var r=t("./lib");n.exports={Graph:r.Graph,json:t("./lib/json"),alg:t("./lib/alg"),version:r.version}},{"./lib":75,"./lib/alg":66,"./lib/json":76}],60:[function(t,n,e){function r(t){function n(o){i.has(r,o)||(r[o]=!0,e.push(o),i.each(t.successors(o),n),i.each(t.predecessors(o),n))}var e,r={},o=[];return i.each(t.nodes(),function(t){e=[],n(t),e.length&&o.push(e)}),o}var i=t("../lodash");n.exports=r},{"../lodash":77}],61:[function(t,n,e){function r(t,n,e){o.isArray(n)||(n=[n]);var r=[],a={};return o.each(n,function(n){if(!t.hasNode(n))throw new Error("Graph does not have node: "+n);i(t,n,"post"===e,a,r)}),r}function i(t,n,e,r,a){o.has(r,n)||(r[n]=!0,e||a.push(n),o.each(t.neighbors(n),function(n){i(t,n,e,r,a)}),e&&a.push(n))}var o=t("../lodash");n.exports=r},{"../lodash":77}],62:[function(t,n,e){function r(t,n,e){return o.transform(t.nodes(),function(r,o){r[o]=i(t,o,n,e)},{})}var i=t("./dijkstra"),o=t("../lodash");n.exports=r},{"../lodash":77,"./dijkstra":63}],63:[function(t,n,e){function r(t,n,e,r){return i(t,String(n),e||u,r||function(n){return t.outEdges(n)})}function i(t,n,e,r){var i,o,u={},c=new a,s=function(t){var n=t.v!==i?t.v:t.w,r=u[n],a=e(t),s=o.distance+a;if(0>a)throw new Error("dijkstra does not allow negative edge weights. Bad edge: "+t+" Weight: "+a);s<r.distance&&(r.distance=s,r.predecessor=i,c.decrease(n,s))};for(t.nodes().forEach(function(t){var e=t===n?0:Number.POSITIVE_INFINITY;u[t]={distance:e},c.add(t,e)});c.size()>0&&(i=c.removeMin(),o=u[i],o.distance!==Number.POSITIVE_INFINITY);)r(i).forEach(s);return u}var o=t("../lodash"),a=t("../data/priority-queue");n.exports=r;var u=o.constant(1)},{"../data/priority-queue":73,"../lodash":77}],64:[function(t,n,e){function r(t){return i.filter(o(t),function(n){return n.length>1||1===n.length&&t.hasEdge(n[0],n[0])})}var i=t("../lodash"),o=t("./tarjan");n.exports=r},{"../lodash":77,"./tarjan":71}],65:[function(t,n,e){function r(t,n,e){return i(t,n||a,e||function(n){return t.outEdges(n)})}function i(t,n,e){var r={},i=t.nodes();return i.forEach(function(t){r[t]={},r[t][t]={distance:0},i.forEach(function(n){t!==n&&(r[t][n]={distance:Number.POSITIVE_INFINITY})}),e(t).forEach(function(e){var i=e.v===t?e.w:e.v,o=n(e);r[t][i]={distance:o,predecessor:t}})}),i.forEach(function(t){var n=r[t];i.forEach(function(e){var o=r[e];i.forEach(function(e){var r=o[t],i=n[e],a=o[e],u=r.distance+i.distance;u<a.distance&&(a.distance=u,a.predecessor=i.predecessor)})})}),r}var o=t("../lodash");n.exports=r;var a=o.constant(1)},{"../lodash":77}],66:[function(t,n,e){n.exports={components:t("./components"),dijkstra:t("./dijkstra"),dijkstraAll:t("./dijkstra-all"),findCycles:t("./find-cycles"),floydWarshall:t("./floyd-warshall"),isAcyclic:t("./is-acyclic"),postorder:t("./postorder"),preorder:t("./preorder"),prim:t("./prim"),tarjan:t("./tarjan"),topsort:t("./topsort")}},{"./components":60,"./dijkstra":63,"./dijkstra-all":62,"./find-cycles":64,"./floyd-warshall":65,"./is-acyclic":67,"./postorder":68,"./preorder":69,"./prim":70,"./tarjan":71,"./topsort":72}],67:[function(t,n,e){function r(t){try{i(t)}catch(n){if(n instanceof i.CycleException)return!1;throw n}return!0}var i=t("./topsort");n.exports=r},{"./topsort":72}],68:[function(t,n,e){function r(t,n){return i(t,n,"post")}var i=t("./dfs");n.exports=r},{"./dfs":61}],69:[function(t,n,e){function r(t,n){return i(t,n,"pre")}var i=t("./dfs");n.exports=r},{"./dfs":61}],70:[function(t,n,e){function r(t,n){function e(t){var e=t.v===r?t.w:t.v,i=s.priority(e);if(void 0!==i){var o=n(t);i>o&&(c[e]=r,s.decrease(e,o))}}var r,u=new o,c={},s=new a;if(0===t.nodeCount())return u;i.each(t.nodes(),function(t){s.add(t,Number.POSITIVE_INFINITY),u.setNode(t)}),s.decrease(t.nodes()[0],0);for(var f=!1;s.size()>0;){if(r=s.removeMin(),i.has(c,r))u.setEdge(r,c[r]);else{if(f)throw new Error("Input graph is not connected: "+t);f=!0}t.nodeEdges(r).forEach(e)}return u}var i=t("../lodash"),o=t("../graph"),a=t("../data/priority-queue");n.exports=r},{"../data/priority-queue":73,"../graph":74,"../lodash":77}],71:[function(t,n,e){function r(t){function n(u){var c=o[u]={onStack:!0,lowlink:e,index:e++};if(r.push(u),t.successors(u).forEach(function(t){i.has(o,t)?o[t].onStack&&(c.lowlink=Math.min(c.lowlink,o[t].index)):(n(t),c.lowlink=Math.min(c.lowlink,o[t].lowlink))}),c.lowlink===c.index){var s,f=[];do s=r.pop(),o[s].onStack=!1,f.push(s);while(u!==s);a.push(f)}}var e=0,r=[],o={},a=[];return t.nodes().forEach(function(t){i.has(o,t)||n(t)}),a}var i=t("../lodash");n.exports=r},{"../lodash":77}],72:[function(t,n,e){function r(t){function n(u){if(o.has(r,u))throw new i;o.has(e,u)||(r[u]=!0,e[u]=!0,o.each(t.predecessors(u),n),delete r[u],a.push(u))}var e={},r={},a=[];if(o.each(t.sinks(),n),o.size(e)!==t.nodeCount())throw new i;return a}function i(){}var o=t("../lodash");n.exports=r,r.CycleException=i},{"../lodash":77}],73:[function(t,n,e){function r(){this._arr=[],this._keyIndices={}}var i=t("../lodash");n.exports=r,r.prototype.size=function(){return this._arr.length},r.prototype.keys=function(){return this._arr.map(function(t){return t.key})},r.prototype.has=function(t){return i.has(this._keyIndices,t)},r.prototype.priority=function(t){var n=this._keyIndices[t];return void 0!==n?this._arr[n].priority:void 0},r.prototype.min=function(){if(0===this.size())throw new Error("Queue underflow");return this._arr[0].key},r.prototype.add=function(t,n){var e=this._keyIndices;if(t=String(t),!i.has(e,t)){var r=this._arr,o=r.length;return e[t]=o,r.push({key:t,priority:n}),this._decrease(o),!0}return!1},r.prototype.removeMin=function(){this._swap(0,this._arr.length-1);var t=this._arr.pop();return delete this._keyIndices[t.key],this._heapify(0),t.key},r.prototype.decrease=function(t,n){var e=this._keyIndices[t];if(n>this._arr[e].priority)throw new Error("New priority is greater than current priority. Key: "+t+" Old: "+this._arr[e].priority+" New: "+n);this._arr[e].priority=n,this._decrease(e)},r.prototype._heapify=function(t){var n=this._arr,e=2*t,r=e+1,i=t;e<n.length&&(i=n[e].priority<n[i].priority?e:i,r<n.length&&(i=n[r].priority<n[i].priority?r:i),i!==t&&(this._swap(t,i),this._heapify(i)))},r.prototype._decrease=function(t){for(var n,e=this._arr,r=e[t].priority;0!==t&&(n=t>>1,!(e[n].priority<r));)this._swap(t,n),t=n},r.prototype._swap=function(t,n){var e=this._arr,r=this._keyIndices,i=e[t],o=e[n];e[t]=o,e[n]=i,r[o.key]=t,r[i.key]=n}},{"../lodash":77}],74:[function(t,n,e){"use strict";function r(t){this._isDirected=s.has(t,"directed")?t.directed:!0,this._isMultigraph=s.has(t,"multigraph")?t.multigraph:!1,this._isCompound=s.has(t,"compound")?t.compound:!1,this._label=void 0,this._defaultNodeLabelFn=s.constant(void 0),this._defaultEdgeLabelFn=s.constant(void 0),this._nodes={},this._isCompound&&(this._parent={},this._children={},this._children[l]={}),this._in={},this._preds={},this._out={},this._sucs={},this._edgeObjs={},this._edgeLabels={}}function i(t,n){s.has(t,n)?t[n]++:t[n]=1}function o(t,n){--t[n]||delete t[n]}function a(t,n,e,r){if(!t&&n>e){var i=n;n=e,e=i}return n+h+e+h+(s.isUndefined(r)?f:r)}function u(t,n,e,r){if(!t&&n>e){var i=n;n=e,e=i}var o={v:n,w:e};return r&&(o.name=r),o}function c(t,n){return a(t,n.v,n.w,n.name)}var s=t("./lodash");n.exports=r;var f="\x00",l="\x00",h="";r.prototype._nodeCount=0,r.prototype._edgeCount=0,r.prototype.isDirected=function(){return this._isDirected},r.prototype.isMultigraph=function(){return this._isMultigraph},r.prototype.isCompound=function(){return this._isCompound},r.prototype.setGraph=function(t){return this._label=t,this},r.prototype.graph=function(){return this._label},r.prototype.setDefaultNodeLabel=function(t){return s.isFunction(t)||(t=s.constant(t)),this._defaultNodeLabelFn=t,this},r.prototype.nodeCount=function(){return this._nodeCount},r.prototype.nodes=function(){return s.keys(this._nodes)},r.prototype.sources=function(){return s.filter(this.nodes(),function(t){return s.isEmpty(this._in[t])},this)},r.prototype.sinks=function(){return s.filter(this.nodes(),function(t){return s.isEmpty(this._out[t])},this)},r.prototype.setNodes=function(t,n){var e=arguments;return s.each(t,function(t){e.length>1?this.setNode(t,n):this.setNode(t)},this),this},r.prototype.setNode=function(t,n){return s.has(this._nodes,t)?(arguments.length>1&&(this._nodes[t]=n),this):(this._nodes[t]=arguments.length>1?n:this._defaultNodeLabelFn(t),this._isCompound&&(this._parent[t]=l,this._children[t]={},this._children[l][t]=!0),this._in[t]={},this._preds[t]={},this._out[t]={},this._sucs[t]={},++this._nodeCount,this)},r.prototype.node=function(t){return this._nodes[t]},r.prototype.hasNode=function(t){return s.has(this._nodes,t)},r.prototype.removeNode=function(t){var n=this;if(s.has(this._nodes,t)){var e=function(t){n.removeEdge(n._edgeObjs[t])};delete this._nodes[t],this._isCompound&&(this._removeFromParentsChildList(t),delete this._parent[t],s.each(this.children(t),function(t){this.setParent(t)},this),delete this._children[t]),s.each(s.keys(this._in[t]),e),delete this._in[t],delete this._preds[t],s.each(s.keys(this._out[t]),e),delete this._out[t],delete this._sucs[t],--this._nodeCount}return this},r.prototype.setParent=function(t,n){if(!this._isCompound)throw new Error("Cannot set parent in a non-compound graph");if(s.isUndefined(n))n=l;else{n+="";for(var e=n;!s.isUndefined(e);e=this.parent(e))if(e===t)throw new Error("Setting "+n+" as parent of "+t+" would create create a cycle");this.setNode(n)}return this.setNode(t),this._removeFromParentsChildList(t),this._parent[t]=n,this._children[n][t]=!0,this},r.prototype._removeFromParentsChildList=function(t){delete this._children[this._parent[t]][t]},r.prototype.parent=function(t){if(this._isCompound){var n=this._parent[t];if(n!==l)return n}},r.prototype.children=function(t){if(s.isUndefined(t)&&(t=l),this._isCompound){var n=this._children[t];if(n)return s.keys(n)}else{if(t===l)return this.nodes();if(this.hasNode(t))return[]}},r.prototype.predecessors=function(t){var n=this._preds[t];return n?s.keys(n):void 0},r.prototype.successors=function(t){var n=this._sucs[t];return n?s.keys(n):void 0},r.prototype.neighbors=function(t){var n=this.predecessors(t);return n?s.union(n,this.successors(t)):void 0},r.prototype.setDefaultEdgeLabel=function(t){return s.isFunction(t)||(t=s.constant(t)),this._defaultEdgeLabelFn=t,this},r.prototype.edgeCount=function(){return this._edgeCount},r.prototype.edges=function(){return s.values(this._edgeObjs)},r.prototype.setPath=function(t,n){var e=this,r=arguments;return s.reduce(t,function(t,i){return r.length>1?e.setEdge(t,i,n):e.setEdge(t,i),i}),this},r.prototype.setEdge=function(){var t,n,e,r,o=!1;s.isPlainObject(arguments[0])?(t=arguments[0].v,n=arguments[0].w,e=arguments[0].name,2===arguments.length&&(r=arguments[1],o=!0)):(t=arguments[0],n=arguments[1],e=arguments[3],arguments.length>2&&(r=arguments[2],o=!0)),t=""+t,n=""+n,s.isUndefined(e)||(e=""+e);var c=a(this._isDirected,t,n,e);if(s.has(this._edgeLabels,c))return o&&(this._edgeLabels[c]=r),this;if(!s.isUndefined(e)&&!this._isMultigraph)throw new Error("Cannot set a